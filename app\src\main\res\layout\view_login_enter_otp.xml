<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:id="@+id/lnrVerifyotpview"
    >

    <LinearLayout
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:layout_alignParentRight="true"
        >

        <TextView
            android:id="@+id/login"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp10"
            android:text="Enter OTP"
            android:textColor="@color/headingColor"
            android:textSize="@dimen/dp22"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp10"
            android:text="we have sent 4 digit code to"
            android:textColor="@color/subHeadingColor"
            android:textSize="@dimen/sp16" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp20"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvMobileNumber"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/dp10"
                android:gravity="center"
                android:text="91"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:visibility="visible" />

            <TextView
                android:id="@+id/editLogin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="EDIT"
                android:textColor="@color/headingColor"
                android:textSize="16sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp20"
            android:gravity="center"
            android:orientation="horizontal">

            <RelativeLayout
                android:layout_width="@dimen/otp_circle_size"
                android:layout_height="@dimen/otp_circle_size"
                android:layout_marginRight="@dimen/dp10"
                android:background="@drawable/ic_gold_trn_circle">

                <EditText
                    android:id="@+id/editText1"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@null"
                    android:gravity="center"
                    android:inputType="number"
                    android:maxLength="2"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="@color/editTextColor"
                    android:textSize="20sp"
                    android:textStyle="bold" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="@dimen/otp_circle_size"
                android:layout_height="@dimen/otp_circle_size"
                android:layout_marginRight="@dimen/dp10"
                android:background="@drawable/ic_gold_trn_circle">

                <EditText
                    android:id="@+id/editText2"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@null"
                    android:gravity="center"
                    android:inputType="number"
                    android:maxLength="2"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="@color/editTextColor"
                    android:textSize="20sp"
                    android:textStyle="bold" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="@dimen/otp_circle_size"
                android:layout_height="@dimen/otp_circle_size"
                android:layout_marginRight="@dimen/dp10"
                android:background="@drawable/ic_gold_trn_circle">

                <EditText
                    android:id="@+id/editText3"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@null"
                    android:gravity="center"
                    android:inputType="number"
                    android:maxLength="2"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="@color/editTextColor"
                    android:textSize="20sp"
                    android:textStyle="bold" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="@dimen/otp_circle_size"
                android:layout_height="@dimen/otp_circle_size"
                android:background="@drawable/ic_gold_trn_circle">

                <EditText
                    android:id="@+id/editText4"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@null"
                    android:gravity="center"
                    android:inputType="number"
                    android:maxLength="1"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="@color/editTextColor"
                    android:textSize="20sp"
                    android:textStyle="bold" />
            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/lnrResentOTP"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp10"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins_medium"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="Didn't receive code?"
                android:textColor="@color/subHeadingColor" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins_bold"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text=" Request again"
                android:textColor="@color/headingColor"
                android:textStyle="normal" />
        </LinearLayout>

        <RelativeLayout
            android:id="@+id/rltVerifycode"
            android:layout_width="220dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="10dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="@dimen/payu_dimen_45dp"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="5dp"
                android:background="@drawable/btn_green_bg"
                android:gravity="center"
                android:text="Verify &amp; Continue"
                android:textColor="@color/login_buttontxtColor"
                android:textSize="@dimen/dp18"
                android:textStyle="bold" />
        </RelativeLayout>
    </LinearLayout>


</RelativeLayout>
