<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/payu_dimen_120dp"
    android:layout_gravity="center"
    android:layout_margin="@dimen/dp15"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="2dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:background="@drawable/background_border"
        android:gravity="center">

        <RelativeLayout
            android:id="@+id/rel_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center">

            <TextView
                android:id="@+id/txt_discount"
                android:layout_width="@dimen/dp40"
                android:layout_height="@dimen/dp20"
                android:layout_marginTop="@dimen/dp2"
                android:layout_marginEnd="@dimen/dp2"
                android:layout_alignParentEnd="true"
                android:background="@drawable/giftheading_bg"
                android:fontFamily="@font/poppins_medium"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="499.00"
                android:textColor="@color/white"
                android:textSize="@dimen/dp10" />

            <ImageView
                android:id="@+id/imalucky"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp20"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp30"
                android:src="@drawable/bulkchipsred" />

            <TextView
                android:id="@+id/txtproname"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/imalucky"
                android:layout_centerHorizontal="true"
                android:lineSpacingExtra="5dp"
                android:text=" 10Cr "
                android:visibility="gone"
                android:textColor="@color/colordullwhite"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/txt_discount_amt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/imalucky"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="8dp"
                android:lineSpacingExtra="5dp"
                android:text=" 10Cr "
                android:textColor="@color/colordullwhite"
                android:textSize="14sp" />


            <TextView
                android:id="@+id/txtAmount"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp25"
                android:layout_alignParentBottom="true"
                android:layout_centerInParent="true"
                android:layout_gravity="center_horizontal"
                android:layout_margin="@dimen/dp2"
                android:background="@drawable/btn_green_bg"
                android:fontFamily="@font/poppins_medium"
                android:gravity="center"
                android:text="0"
                android:textColor="@color/white"
                android:textSize="@dimen/dp15" />

        </RelativeLayout>


    </RelativeLayout>
    <!--    </androidx.cardview.widget.CardView>-->

</RelativeLayout>
