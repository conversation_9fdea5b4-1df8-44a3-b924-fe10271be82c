<?xml version="1.1" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fillViewport="true"
    android:scrollbars="none">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@android:color/white"
    android:gravity="center"
    android:minWidth="300dp">

    <!-- Title -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Select Wallet Option"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:layout_marginBottom="8dp"
        android:gravity="center" />

    <!-- Subtitle -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Choose your preferred payment method"
        android:textSize="14sp"
        android:textColor="@android:color/darker_gray"
        android:layout_marginBottom="20dp"
        android:gravity="center" />

    <!-- USDT Option - Portrait Optimized -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@android:color/white"
        android:elevation="3dp"
        android:padding="16dp"
        android:layout_marginBottom="12dp"
        android:gravity="center"
        android:id="@+id/layout_usdt">

        <!-- USDT Icon and Title Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <ImageView
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:src="@drawable/usdt"
                android:layout_marginEnd="12dp"
                android:scaleType="centerInside" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="USDT Wallet"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/black" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Cryptocurrency payments"
                    android:textSize="13sp"
                    android:textColor="@android:color/darker_gray" />

            </LinearLayout>

        </LinearLayout>

        <!-- USDT Select Button -->
        <Button
            android:id="@+id/btn_usdt"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:text="Select USDT"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@android:color/holo_green_dark"
            android:layout_marginTop="8dp" />

    </LinearLayout>

    <!-- RBM Option - Portrait Optimized -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@android:color/white"
        android:elevation="3dp"
        android:padding="16dp"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:id="@+id/layout_rbm">

        <!-- RBM Icon and Title Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <ImageView
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:src="@drawable/rbm"
                android:layout_marginEnd="12dp"
                android:scaleType="centerInside" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="RBM Wallet"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/black" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Traditional payment method"
                    android:textSize="13sp"
                    android:textColor="@android:color/darker_gray" />

            </LinearLayout>

        </LinearLayout>

        <!-- RBM Select Button -->
        <Button
            android:id="@+id/btn_rbm"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:text="Select RBM"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@android:color/holo_orange_dark"
            android:layout_marginTop="8dp" />

    </LinearLayout>

    <!-- Important Warning Notice -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="8dp"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:background="#FFEBEE"
        android:elevation="3dp"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="⚠️ IMPORTANT NOTICE"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="#D32F2F"
            android:gravity="center"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="This is a one-time selection and cannot be changed later. Please choose your wallet option carefully."
            android:textSize="12sp"
            android:textColor="#D32F2F"
            android:gravity="center"
            android:lineSpacingExtra="3dp" />

    </LinearLayout>

</LinearLayout>

</ScrollView>
