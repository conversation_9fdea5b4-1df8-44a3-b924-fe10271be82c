<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center"
    >

    <RelativeLayout
        android:id="@+id/rltContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dimen_10dp"
        android:layout_marginBottom="5dp"
        android:layout_centerHorizontal="true"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/ivContainerbg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignLeft="@id/lnrJackportamountparent"
            android:layout_alignTop="@id/lnrJackportamountparent"
            android:layout_alignRight="@id/lnrJackportamountparent"
            android:layout_alignBottom="@id/lnrJackportamountparent"
            android:background="@drawable/ic_jackpot_rule_bg" />

        <LinearLayout
            android:id="@+id/lnrJackportamountparent"
            android:layout_width="95dp"
            android:layout_height="70dp"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            >

            <TextView
                android:id="@+id/tvJackpotHeading"
                style="@style/ShadowWhiteTextview"
                android:alpha="0.7"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="3dp"
                android:text="SET"
                android:textSize="12sp"
                android:textAllCaps="true"
                android:textStyle="bold"
                android:gravity="center"
                />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingRight="10dp"
                android:paddingLeft="10dp"
                android:orientation="vertical"
                android:gravity="center"
                >
                <TextView
                    android:id="@+id/tvJackpotamount"
                    style="@style/ShadowWhiteTextview"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="2dp"
                    android:text="0000"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="?dividerHorizontal"
                    android:layout_marginBottom="2dp"
                    />

                <TextView
                    android:id="@+id/tvJackpotSelectamount"
                    style="@style/ShadowWhiteTextview"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="14sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

        <RelativeLayout
            android:id="@+id/rltAmountadded"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_centerHorizontal="true"
            android:layout_below="@+id/lnrJackportamountparent"
            android:layout_marginTop="-25dp"
            android:visibility="gone"
            >

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/ic_hightlight"
                android:layout_alignRight="@id/tvUsersAddedAmount"
                android:layout_alignLeft="@id/tvUsersAddedAmount"
                android:layout_alignTop="@id/tvUsersAddedAmount"
                android:layout_alignBottom="@id/tvUsersAddedAmount"
                android:alpha="0.4"
                />

            <TextView
                android:id="@+id/tvUsersAddedAmount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                style="@style/ShadowGoldTextview"
                android:textSize="18sp"
                android:paddingRight="10dp"
                android:paddingLeft="10dp"
                />

        </RelativeLayout>
    </RelativeLayout>

    <Button
        android:id="@+id/btninto"
        style="@style/ShadowWhiteTextview"
        android:layout_width="95dp"
        android:layout_height="33dp"
        android:layout_below="@+id/rltContainer"
        android:layout_centerHorizontal="true"
        android:background="@drawable/ic_jackpot_btn_gold"
        android:text="2X"
        android:textColor="@color/black"
        android:textSize="16sp" />

</RelativeLayout>
