<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    style="@style/dialogParentStyle">


    <ImageView
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    style="@style/popUpBoxbg"
    android:layout_alignLeft="@id/lnr_box"
    android:layout_alignRight="@id/lnr_box"
    android:layout_alignTop="@id/lnr_box"
    android:layout_alignBottom="@id/lnr_box"
    />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:id="@+id/lnr_box"
        android:layout_below="@+id/rtl_toolbar"
        android:layout_marginTop="@dimen/pop_up_top_margin"
        android:layout_marginHorizontal="25dp"
        android:padding="20dp"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="@dimen/dp20"
            android:paddingBottom="@dimen/dp20"
            android:layout_marginBottom="@dimen/dp20"
            >

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dp10"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center"
                >

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_alignLeft="@+id/lnrRuleslist"
                    android:layout_alignTop="@+id/lnrRuleslist"
                    android:layout_alignRight="@+id/lnrRuleslist"
                    android:layout_alignBottom="@+id/lnrRuleslist"
                    android:background="@drawable/d_jackpot_rulebg" />

                <LinearLayout
                    android:id="@+id/lnrRuleslist"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:padding="5dp">

                        <TextView
                            style="@style/ShadowGoldTextview"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="Time"
                            android:textStyle="bold" />

                        <View
                            android:layout_width="3dp"
                            android:layout_height="match_parent"
                            android:background="?dividerHorizontal" />

                        <TextView
                            style="@style/ShadowGoldTextview"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="Rewards" />

                        <View
                            android:layout_width="3dp"
                            android:layout_height="match_parent"
                            android:background="?dividerHorizontal" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center">
                            <TextView
                                style="@style/ShadowGoldTextview"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:text="Type" />

                        </LinearLayout>

                        <View
                            android:layout_width="3dp"
                            android:layout_height="match_parent"
                            android:background="?dividerHorizontal" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center">

                            <TextView
                                style="@style/ShadowGoldTextview"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:text="Big Winner" />
                        </LinearLayout>

                        <View
                            android:layout_width="3dp"
                            android:layout_height="match_parent"
                            android:background="?dividerHorizontal" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="Winner"
                            style="@style/ShadowGoldTextview"
                            android:textStyle="bold" />
                    </LinearLayout>
                </LinearLayout>
            </RelativeLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recJackpotHistory"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:itemCount="2"
                tools:listitem="@layout/item_jackpothistory"
                />

        </LinearLayout>
    </LinearLayout>



    <include
        layout="@layout/dialog_toolbar"/>


</RelativeLayout>
