<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/gray">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp10">

        <androidx.cardview.widget.CardView
            style="@style/colorLastWinHeadingbg"
            android:layout_weight="1.5"
            app:cardBackgroundColor="@color/gray">

            <TextView
                android:id="@+id/idd"
                style="@style/colorLastWinHeadingText"
                android:text=""
                android:textColor="@color/btn_green" />

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            style="@style/colorLastWinHeadingbg"
            android:layout_weight="1"
            app:cardBackgroundColor="@color/gray">

            <TextView
                android:id="@+id/amountt"
                style="@style/colorLastWinHeadingText"
                android:text=""
                android:textColor="@color/btn_green"

                />

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            style="@style/colorLastWinHeadingbg"
            android:layout_weight="1"
            app:cardBackgroundColor="@color/gray">

            <TextView
                android:id="@+id/bett"
                style="@style/colorLastWinHeadingText"
                android:text=""
                android:textColor="@color/btn_green" />

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            style="@style/colorLastWinHeadingbg"
            android:layout_height="@dimen/dp30"
            android:layout_weight="1.5"
            app:cardBackgroundColor="@color/gray">

            <TextView
                android:id="@+id/win"
                style="@style/colorLastWinHeadingText"
                android:text=""
                android:textColor="@color/btn_green" />

        </androidx.cardview.widget.CardView>

        <!--        <androidx.cardview.widget.CardView-->
        <!--            android:layout_weight="1"-->
        <!--            app:cardBackgroundColor="@color/white"-->
        <!--            style="@style/colorLastWinHeadingbg"-->
        <!--            >-->

        <!--            <TextView-->
        <!--                android:id="@+id/tvFiled4"-->
        <!--                style="@style/colorLastWinHeadingText"-->
        <!--                android:text="Number"-->
        <!--                android:textColor="@color/btn_green"-->
        <!--                />-->

        <!--        </androidx.cardview.widget.CardView>-->
    </LinearLayout>

</LinearLayout>
