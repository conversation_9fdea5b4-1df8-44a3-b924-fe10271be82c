<?xml version="1.1" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="300dp"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    app:cardBackgroundColor="@color/white"
    app:cardElevation="10dp"
    app:cardCornerRadius="@dimen/dimen_20dp"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingVertical="@dimen/dp10"
        android:paddingHorizontal="@dimen/dp20"
        >

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_lock"
            android:layout_marginBottom="@dimen/dp15"
            />

        <TextView
            android:id="@+id/tvHeading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="Restricted State"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:layout_marginBottom="@dimen/dp15"
            />

        <TextView
            android:id="@+id/tvDescription"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="For your security, this state is currently unavailable"
            android:textColor="@color/gray"
            android:gravity="center"
            android:textSize="11sp"
            android:layout_marginBottom="@dimen/dp25"
            />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/btn1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="Learn more"
                android:textColor="@color/red"
                android:textStyle="bold"
                android:textSize="12sp"
                android:layout_weight="1"
                android:visibility="invisible"
                />

            <TextView
                android:id="@+id/btn2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ok"
                android:textColor="@color/red"
                android:textStyle="bold"
                android:textSize="12sp"

                />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
