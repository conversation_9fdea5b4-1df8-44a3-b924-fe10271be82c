<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/relativeChip"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingLeft="5dp"
    android:paddingRight="@dimen/dp5"
    android:paddingTop="5dp">

    <RelativeLayout
        android:id="@+id/rltmainview"
        android:layout_width="@dimen/dp30"
        android:layout_height="@dimen/dp30">

        <TextView
            android:id="@+id/txt_cat"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:ellipsize="end"
            android:padding="5dp"
            android:shadowColor="@color/black"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="3"
            android:text=""
            android:textColor="@color/colorPrimary"
            android:textSize="0dp"
            android:textStyle="bold" />
    </RelativeLayout>
</RelativeLayout>
