<?xml version="1.1" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.example.sms_readpayment">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
        android:allowBackup="true"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        tools:replace="android:theme"
        android:theme="@style/Theme.SMS_ReadPayment"
        android:usesCleartextTraffic="true">
        <activity
            android:name=".CheckPaymentStatus"
            android:theme="@style/Themeapp"
            android:exported="false" />
        <activity
            android:name=".MainActivity"
            android:exported="false" />
        <activity
            android:name=".InitiatePayment"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

<!--                <category android:name="android.intent.category.LAUNCHER" />-->
            </intent-filter>
        </activity>
        <!-- <receiver android:name=".SmsListener" -->
        <!-- android:exported="true"> -->
        <!-- <intent-filter> -->
        <!-- <action android:name="android.provider.Telephony.SMS_RECEIVED" /> -->
        <!-- </intent-filter> -->
        <!-- </receiver> -->
<!--        <receiver-->
<!--            android:name=".SmsListener"-->
<!--            android:exported="true">-->
<!--            <intent-filter android:priority="1000">-->
<!--                <action android:name="android.provider.Telephony.SMS_RECEIVED" />-->
<!--            </intent-filter>-->
<!--        </receiver>-->
    </application>

</manifest>
