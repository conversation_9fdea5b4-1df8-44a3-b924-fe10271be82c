<?xml version="1.1" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:layout_gravity="center"
    android:gravity="center"
    android:background="@drawable/splash"
    >

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:src="@drawable/gift_open"
        />

    <TextView
        android:id="@+id/txtwincoins"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="Congratulations you win 1000 coins!"
        android:textSize="18sp"
        android:layout_gravity="center"
        android:gravity="center"
        android:textColor="@color/Golder_yellow"
        android:textStyle="bold"
        />


</LinearLayout>
