<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">


    <RelativeLayout
        android:id="@+id/lnrtypegame"
        android:layout_width="wrap_content"
        android:layout_height="95dp"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginTop="@dimen/dp60"
        android:background="@drawable/ic_dt_bottom_strip"
        android:orientation="horizontal"
        android:visibility="visible">
        <!--  Player 1 START -->
        <RelativeLayout
            android:id="@+id/rltplayer1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/dimen_7dp">

            <RelativeLayout
                android:id="@+id/rltcirclproimage1"
                android:layout_width="@dimen/dp60"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp20"
                android:layout_marginTop="@dimen/dp40">

                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/imgpl1circle"
                    android:layout_width="@dimen/dp60"
                    android:layout_height="@dimen/dp50"
                    android:layout_centerVertical="true"
                    android:src="@drawable/avatar_ic" />
            </RelativeLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:layout_toRightOf="@+id/rltcirclproimage1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/txtName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/imgpl1circle"
                    android:layout_marginLeft="@dimen/dp10"
                    android:layout_marginTop="@dimen/dp30"
                    android:ellipsize="end"
                    android:shadowColor="@color/black"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="3"
                    android:text="asif"
                    android:textColor="@color/white"
                    android:textSize="15dp"
                    android:textStyle="bold" />

                <RelativeLayout
                    android:id="@+id/ChipstoUser"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/lnrProfileDetails"
                    android:layout_marginBottom="@dimen/dimen_10dp"
                    android:layout_toRightOf="@+id/rltcirclproimage1">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_alignLeft="@id/txtBallence"
                        android:layout_alignTop="@id/txtBallence"
                        android:layout_alignRight="@id/txtBallence"
                        android:layout_alignBottom="@id/txtBallence"
                        android:scaleType="fitXY"
                        android:src="@drawable/player_wallet" />

                    <TextView
                        android:id="@+id/txtBallence"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp40"
                        android:layout_marginRight="2dp"
                        android:gravity="center_vertical"
                        android:minWidth="110dp"
                        android:paddingLeft="@dimen/dp15"
                        android:text="0"
                        android:textColor="@color/gold_color"
                        android:textSize="14dp"
                        android:textStyle="bold" />

                    <ImageView
                        android:id="@+id/iv_add"
                        android:layout_width="45dp"
                        android:layout_height="24dp"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="-20dp"
                        android:layout_toRightOf="@+id/txtBallence"
                        android:background="@drawable/gold_add_btn"
                        android:onClick="openBuyChipsActivity"
                        tools:ignore="SpeakableTextPresentCheck,TouchTargetSizeCheck" />
                    <!--  <ImageView
                        android:id="@+id/iv_add"
                        android:layout_width="45dp"
                        android:layout_height="24dp"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="-20dp"
                        android:layout_toRightOf="@+id/txtBallence"
                        android:background="@drawable/gold_add_btn"
                        android:onClick="openBuyChipsActivity" />
       -->
                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/lnrProfileDetails"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_toRightOf="@+id/rltcirclproimage1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:shadowColor="@color/black"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="3"
                    android:text="asif"
                    android:textColor="@color/white"
                    android:textSize="13dp"
                    android:textStyle="bold"
                    android:visibility="invisible" />

                <TextView
                    android:id="@+id/txt_gameId"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:shadowColor="@color/black"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="3"
                    android:text="asif"
                    android:textSize="13dp"
                    android:textStyle="bold"
                    android:visibility="gone" />
            </LinearLayout>
        </RelativeLayout>
        <!--  Player 1 END -->
        <HorizontalScrollView
            android:id="@+id/scrollView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginBottom="@dimen/dimen_5dp"
            android:layout_toRightOf="@+id/rltplayer1"
            android:scrollbars="none"
            tools:ignore="SpeakableTextPresentCheck">


            <LinearLayout
                android:id="@+id/lnrfollow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp20"
                android:layout_marginTop="@dimen/dp35"
                android:layout_marginBottom="@dimen/dimen_5dp"
                android:orientation="horizontal">
                <!--                    <include layout="@layout/cat_txtview" />-->
            </LinearLayout>
        </HorizontalScrollView>

        <TextView
            android:id="@+id/tvStartTimer"
            style="@style/ShadowGoldTextview"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="350dp"
            android:layout_marginTop="60dp"
            android:layout_toRightOf="@+id/rltplayer1"
            android:shadowColor="@color/black"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="3"
            android:text="30s"
            android:textColor="@color/white"
            android:textSize="@dimen/jackpot_heading_size"
            android:visibility="gone" />

        <TextView
            android:id="@+id/txtcountdown"
            style="@style/ShadowGoldTextview"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="250dp"
            android:layout_marginTop="50dp"
            android:layout_toRightOf="@+id/tvStartTimer"
            android:shadowColor="@color/black"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="3"
            android:text=""
            android:textColor="@color/white"
            android:textSize="@dimen/jackpot_heading_size" />

        <LinearLayout
            android:id="@+id/lnrAmountview"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="20dp"
            android:gravity="right"
            android:orientation="vertical"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="45dp"
                android:layout_marginBottom="5dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/imgpl1minus"
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/minusnew"
                    android:visibility="visible" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:layout_alignParentRight="true">

                        <ImageView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_alignLeft="@id/btGameAmount"
                            android:layout_alignTop="@id/btGameAmount"
                            android:layout_alignRight="@id/btGameAmount"
                            android:layout_alignBottom="@id/btGameAmount"
                            android:scaleType="fitXY"
                            android:src="@drawable/textboxchal" />

                        <Button
                            android:id="@+id/btGameAmount"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_vertical"
                            android:text="50"
                            android:textColor="#00BAB0"
                            android:textSize="12dp" />
                    </RelativeLayout>
                </LinearLayout>

                <ImageView
                    android:id="@+id/imgpl1plus"
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:src="@drawable/addnew"
                    android:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="visible">

                <LinearLayout
                    android:id="@+id/lnrButtons"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <Button
                        android:id="@+id/btnconfirm"
                        android:layout_width="@dimen/ab_button_width"
                        android:layout_height="35dp"
                        android:background="@drawable/yellow_dark_button"
                        android:onClick="confirmBooking"
                        android:padding="5dp"
                        android:text="CONFIRM"
                        android:textSize="12dp"
                        android:visibility="visible" />
                </LinearLayout>

                <Button
                    android:id="@+id/btnCANCEL"
                    android:layout_width="@dimen/ab_button_width"
                    android:layout_height="35dp"
                    android:layout_above="@+id/lnrtypegame"
                    android:layout_marginLeft="8dp"
                    android:background="@drawable/blue_button"
                    android:onClick="cancelBooking"
                    android:text="CANCEL"
                    android:textSize="12dp" />
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>

</RelativeLayout>
