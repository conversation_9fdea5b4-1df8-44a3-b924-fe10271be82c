<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/background_layout"
    android:gravity="center_vertical"
    android:background="@drawable/buildbg"
    android:orientation="vertical"
    android:theme="@style/Theme.MaterialComponents.DayNight.DarkActionBar"
    tools:context=".InitiatePayment">

    <LinearLayout
        android:id="@+id/layout_profile"
        android:gravity="center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/txt_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="UPI Payment Gateway by Androapps"
            android:textStyle="bold"
            android:layout_marginBottom="25dp"
            android:textColor="#333"
            android:textSize="20sp" />

        <LinearLayout
            android:id="@+id/login_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_centerInParent="true"
            android:layout_marginRight="20dp"
            android:layout_marginLeft="20dp"
            android:gravity="center">
            <androidx.cardview.widget.CardView
                android:id="@+id/cd_login"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="7dp"
                app:cardElevation="0dp"
                app:cardUseCompatPadding="true">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp"
                    android:orientation="vertical">
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="15dp"
                        android:fontFamily="sans-serif"
                        android:textAlignment="center"
                        android:text="@string/please_enter_upi_details_from_which_you_are_going_to_pay_the_amount"
                        android:textColor="@color/black"
                        android:textSize="16sp" />
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="15dp"
                        android:fontFamily="sans-serif"
                        android:textAlignment="center"
                        android:text="@string/upi"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Amount"
                        android:visibility="gone"
                        app:passwordToggleEnabled="false">
                        <!--this is the actual edit text which takes the input-->
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_amt"
                            android:maxLength="5"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:textColor="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:hint="UPI Id"
                        app:passwordToggleEnabled="false">
                        <!--this is the actual edit text which takes the input-->
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_upi_id"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:textColor="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        android:layout_marginTop="10dp"
                        android:hint="User Id">
                        <!--this is the actual edit text which takes the input-->
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_user_id"
                            android:textColor="@color/black"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />
                    </com.google.android.material.textfield.TextInputLayout>
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:visibility="gone"
                        android:hint="Receiver UPI Id">
                        <!--this is the actual edit text which takes the input-->
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_user_id_rec"
                            android:textColor="@color/black"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />
                    </com.google.android.material.textfield.TextInputLayout>
                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp">
                        <!--                        <com.google.android.material.textfield.TextInputLayout-->
                        <!--                            android:layout_width="match_parent"-->
                        <!--                            android:layout_height="wrap_content"-->
                        <!--                            app:hintTextAppearance="@style/TextAppearence.App.TextInputLayout">-->
                        <!--                            <EditText-->
                        <!--                                android:id="@+id/edt_password"-->
                        <!--                                android:layout_width="match_parent"-->
                        <!--                                android:layout_height="45dp"-->
                        <!--                                android:inputType="textPassword"-->
                        <!--                                android:textSize="15sp"-->
                        <!--                                android:hint=""-->
                        <!--                                android:textColor="#333" />-->
                        <!--                        </com.google.android.material.textfield.TextInputLayout>-->
                        <com.google.android.material.textfield.TextInputLayout
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            app:passwordToggleEnabled="false"
                            android:layout_height="wrap_content"
                            android:visibility="gone"
                            android:hint="Name">
                            <!--this is the actual edit text which takes the input-->
                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_name"
                                android:textColor="@color/black"
                                android:inputType="textEmailAddress"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content" />
                        </com.google.android.material.textfield.TextInputLayout>
                        <!--                        <ImageView-->
                        <!--                            android:id="@+id/show_pass_btn"-->
                        <!--                            android:layout_width="40dp"-->
                        <!--                            android:layout_height="40dp"-->
                        <!--                            android:layout_alignParentRight="true"-->
                        <!--                            android:layout_centerVertical="true"-->
                        <!--                            android:layout_marginRight="5dp"-->
                        <!--                            android:alpha=".5"-->
                        <!--                            android:paddingRight="16dp" />-->
                    </RelativeLayout>
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:visibility="gone"
                        android:hint="Note">
                        <!--this is the actual edit text which takes the input-->
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_note"
                            android:textColor="@color/black"
                            android:inputType="text"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <Button
                        android:id="@+id/btn_submit"
                        android:layout_width="200dp"
                        android:layout_height="55dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="25dp"
                        android:text="submit" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>
