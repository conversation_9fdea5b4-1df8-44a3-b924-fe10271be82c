<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp10"
        android:background="@drawable/d_background_border_white"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/dp5"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dp5">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/txt_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/app_name"
                        android:textColor="@color/white"
                        android:textSize="@dimen/dimen_18sp" />

                    <TextView
                        android:id="@+id/txt_details"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:gravity="end"
                        android:text="Details >"
                        android:textColor="@color/gray_bg"
                        android:textSize="@dimen/dimen_16sp" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="@dimen/payu_dimen_28dp"
                        android:layout_height="@dimen/payu_dimen_28dp"
                        android:src="@drawable/gold_cup" />

                    <TextView
                        android:id="@+id/txt_price"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="₹72"
                        android:textColor="@color/white"
                        android:textSize="@dimen/dimen_22sp" />

                    <TextView
                        android:id="@+id/txt_entry_fee"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:gravity="end"
                        android:text="Entry: ₹5"
                        android:textColor="@color/orange"
                        android:textSize="@dimen/dimen_16sp" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="₹72"
                        android:textColor="@color/white"
                        android:textSize="@dimen/dimen_22sp"
                        android:visibility="invisible" />

                    <TextView
                        android:id="@+id/txt_join"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_weight=".6"
                        android:background="@drawable/btn_green_bg"
                        android:paddingTop="@dimen/dp2"
                        android:text="Join"
                        android:textAlignment="center"
                        android:textColor="@color/white"
                        android:textSize="@dimen/dimen_16sp"
                        android:textStyle="bold" />
                </LinearLayout>
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#ffffff" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.cardview.widget.CardView
                    style="@style/colorLastWinHeadingbg"
                    android:layout_weight="1"
                    app:cardBackgroundColor="@color/transparent">

                    <TextView
                        style="@style/colorLastWinHeadingText"
                        android:text="Winners"
                        android:textSize="13sp" />

                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    style="@style/colorLastWinHeadingbg"
                    android:layout_weight="1"
                    app:cardBackgroundColor="@color/transparent">

                    <TextView
                        style="@style/colorLastWinHeadingText"
                        android:text="Seats"
                        android:textSize="13sp" />

                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    style="@style/colorLastWinHeadingbg"
                    android:layout_weight="1"
                    app:cardBackgroundColor="@color/transparent">

                    <TextView
                        style="@style/colorLastWinHeadingText"
                        android:text="Format"
                        android:textSize="13sp" />

                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    style="@style/colorLastWinHeadingbg"
                    android:layout_weight="1.6"
                    app:cardBackgroundColor="@color/transparent">

                    <TextView
                        style="@style/colorLastWinHeadingText"
                        android:text="Starts In"
                        android:textSize="13sp" />

                </androidx.cardview.widget.CardView>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.cardview.widget.CardView
                    style="@style/colorLastWinHeadingbg"
                    android:layout_weight="1"
                    app:cardBackgroundColor="@color/transparent">

                    <TextView
                        android:id="@+id/winner"
                        style="@style/colorLastWinHeadingText"
                        android:text="0"
                        android:textSize="13sp" />

                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    style="@style/colorLastWinHeadingbg"
                    android:layout_weight="1"
                    app:cardBackgroundColor="@color/transparent">

                    <TextView
                        android:id="@+id/txt_no_participants"
                        style="@style/colorLastWinHeadingText"
                        android:text="0"
                        android:textSize="13sp" />

                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    style="@style/colorLastWinHeadingbg"
                    android:layout_weight="1"
                    app:cardBackgroundColor="@color/transparent">

                    <TextView
                        style="@style/colorLastWinHeadingText"
                        android:text="Point"
                        android:textSize="13sp" />

                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    style="@style/colorLastWinHeadingbg"
                    android:layout_weight="1.6"
                    app:cardBackgroundColor="@color/transparent">

                    <TextView
                        android:id="@+id/txt_start_time"
                        style="@style/colorLastWinHeadingText"
                        android:text="Today at\n00:00:00"
                        android:textAlignment="center"
                        android:textColor="@color/yellow1"
                        android:textSize="13sp" />

                </androidx.cardview.widget.CardView>
            </LinearLayout>

        </LinearLayout>
    </LinearLayout>
</LinearLayout>
