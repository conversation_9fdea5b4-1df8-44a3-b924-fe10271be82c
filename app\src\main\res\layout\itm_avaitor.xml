<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:padding="10dp"
    >

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        >
    <RelativeLayout
        android:id="@+id/rltSelectedview"
        android:layout_width="75dp"
        android:layout_height="75dp"
        android:gravity="center"
        android:background="@drawable/glow_circle_bg"
        android:layout_marginLeft="@dimen/dp5"
        />

    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/ivUserAvaitor"
        android:layout_width="65dp"
        android:layout_height="65dp"
        android:src="@drawable/avatar"
        android:layout_centerInParent="true"
        />
    </RelativeLayout>
</RelativeLayout>
