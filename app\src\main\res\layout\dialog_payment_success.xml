<?xml version="1.1" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="16dp"
    app:cardElevation="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="16dp"
            android:src="@drawable/ic_success" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:gravity="center"
            android:text="Payment Successful!"
            android:textColor="@color/green"
            android:textSize="20sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:text="Your payment has been processed successfully"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tvSuccessAmount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:text="₹100"
            android:textColor="@color/colorPrimary"
            android:textSize="24sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvTransactionId"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:text="Transaction ID: 123456789"
            android:textColor="@color/black"
            android:textSize="14sp" />

        <Button
            android:id="@+id/btnDone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:background="@drawable/button_bg"
            android:text="Done"
            android:textColor="@android:color/white" />
    </LinearLayout>
</androidx.cardview.widget.CardView>
