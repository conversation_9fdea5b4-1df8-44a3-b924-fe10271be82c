<?xml version="1.1" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="10dp"
    >

    <RelativeLayout
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:padding="5dp"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/payu_dimen_55dp"
                android:background="@drawable/glow_effect_dragon"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:paddingTop="8dp"
                android:paddingBottom="8dp"
                android:layout_marginBottom="20dp"
                >

                <TextView
                    android:id="@+id/tv_heading"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="TITTLE"
                    android:paddingLeft="@dimen/dp7"
                    android:paddingTop="@dimen/dp7"
                    android:layout_gravity="center_vertical"
                    android:textAllCaps="true"
                    android:textColor="@color/black"
                    android:textSize="18sp"
                    android:layout_weight="1"
                    app:fontFilePath="@string/Helvetica_Bold_Extra"
                    />

                <ImageView
                    android:id="@+id/ivClose"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_close_new"
                    />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_subheading"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Are you sure you want to drop ? You will lose this game by 20 points."
                android:textSize="16sp"
                android:layout_weight="1"
                app:fontFilePath="@string/Helvetica_Bold_Extra"
                android:gravity="center"
                android:layout_gravity="center"
                android:textColor="@color/black"
                android:layout_marginBottom="15dp"
                android:layout_marginHorizontal="10dp"
                />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginBottom="10dp"
                >

                <androidx.cardview.widget.CardView
                    android:id="@+id/btn_yes"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    app:cardCornerRadius="5dp"
                    android:layout_marginRight="10dp"
                    >

                    <RelativeLayout
                        android:layout_width="@dimen/dp100"
                        android:layout_height="@dimen/payu_dimen_45dp"
                        android:paddingRight="20dp"
                        android:paddingLeft="20dp"
                        android:background="@drawable/glow_effect_tie"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp"
                        >

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="YES"
                            android:textAllCaps="true"
                            android:textColor="@color/white"
                            android:textSize="18sp"
                            app:fontFilePath="@string/Helvetica_Bold_Extra" />


                    </RelativeLayout>

                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/bt_no"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    app:cardCornerRadius="5dp"
                    android:layout_marginLeft="10dp"
                    >

                    <RelativeLayout
                        android:layout_width="@dimen/dp100"
                        android:layout_height="@dimen/payu_dimen_45dp"
                        android:paddingRight="20dp"
                        android:background="@drawable/glow_effect_tiger"
                        android:paddingLeft="20dp"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp"
                        >

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="NO"
                            android:textSize="18sp"
                            android:layout_weight="1"
                            app:fontFilePath="@string/Helvetica_Bold_Extra"
                            android:gravity="center"
                            android:layout_gravity="center"
                            android:textColor="@color/white"
                            android:textAllCaps="true"
                            />


                    </RelativeLayout>

                </androidx.cardview.widget.CardView>


            </LinearLayout>

        </LinearLayout>


    </RelativeLayout>

</androidx.cardview.widget.CardView>
