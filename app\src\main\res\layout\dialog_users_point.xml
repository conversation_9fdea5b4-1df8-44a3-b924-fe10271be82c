<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@android:color/transparent">


    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:cardCornerRadius="10dp"
        app:cardBackgroundColor="@color/transparent"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:background="@drawable/ic_close_bg_box"
            >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:layout_marginTop="15dp"
                >

                <TextView
                    android:id="@+id/txt_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_gravity="center_vertical"
                    android:text="@string/game_points_game_id"
                    android:textColor="@color/white"
                    android:textSize="@dimen/heading_size"
                    android:textStyle="bold"
                    app:fontFilePath="@string/Helvetica_Bold_Extra"
                    android:gravity="center"
                    />

                <ImageView
                    android:id="@+id/imgclosetop"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:src="@drawable/ic_close_new"
                    android:visibility="visible"
                    android:elevation="2dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="10dp"
                    />


            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:id="@+id/lnr_box"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_above="@+id/lnr_bottom"
                    android:layout_marginTop="-10dp"
                    android:layout_marginBottom="-10dp"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingLeft="4dp"
                    android:paddingTop="10dp"
                    android:paddingRight="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="10dp"
                        android:orientation="vertical">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rec_user_points"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            tools:listitem="@layout/item_user_point" />
                    </LinearLayout>
                </LinearLayout>

            <LinearLayout
                android:id="@+id/lnr_bottom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#035C08"
                android:elevation="2dp"
                android:layout_gravity="bottom"
                android:gravity="bottom"
                android:layout_alignParentBottom="true"
                >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_gravity="center"
                    >

                    <TextView
                        android:id="@+id/txt_timer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:text="@string/get_ready_next_game_start_in"
                        android:textColor="@color/white"
                        android:gravity="center"
                        />

                </LinearLayout>


            </LinearLayout>
            </RelativeLayout>

        </LinearLayout>
    </androidx.cardview.widget.CardView>


</RelativeLayout>
