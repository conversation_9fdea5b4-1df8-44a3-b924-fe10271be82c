package com.gamegards.luckyrbm.RedeemCoins;


public class RedeemModel {
    public String id;
    public String user_id;
    public String redeem_id;
    public String coin;
    public String mobile;
    public String status;
    public String created_date;
    public String updated_date;
    public String isDeleted;
    public String user_name;
    public String user_mobile;
    public int ViewType = 0;

    public static final int GAME_LIST = 0;
    public static final int TRANSACTION_LIST = 1;
    public static final int REDEEM_LIST = 3;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUser_id() {
        return user_id;
    }

    public void setUser_id(String user_id) {
        this.user_id = user_id;
    }

    public String getRedeem_id() {
        return redeem_id;
    }

    public void setRedeem_id(String redeem_id) {
        this.redeem_id = redeem_id;
    }

    public String getCoin() {
        return coin;
    }

    public void setCoin(String coin) {
        this.coin = coin;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreated_date() {
        return created_date;
    }

    public void setCreated_date(String created_date) {
        this.created_date = created_date;
    }

    public String getUpdated_date() {
        return updated_date;
    }

    public void setUpdated_date(String updated_date) {
        this.updated_date = updated_date;
    }

    public String getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getUser_name() {
        return user_name;
    }

    public void setUser_name(String user_name) {
        this.user_name = user_name;
    }

    public String getUser_mobile() {
        return user_mobile;
    }

    public void setUser_mobile(String user_mobile) {
        this.user_mobile = user_mobile;
    }
}