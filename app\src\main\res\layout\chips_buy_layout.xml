<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="200dp"
    android:orientation="vertical"
    android:layout_gravity="center"
    android:gravity="center"
    android:padding="2dp"
    android:layout_margin="10dp"
    >

    <RelativeLayout
        android:layout_width="150dp"
        android:layout_height="170dp"
        android:layout_gravity="center"
        android:background="@drawable/frame"
        android:gravity="center"
        >

        <RelativeLayout
            android:id="@+id/rel_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center">

            <ImageView
                android:id="@+id/imalucky"
                android:layout_width="wrap_content"
                android:layout_height="55dp"
                android:layout_centerInParent="true"
                android:src="@drawable/bulkchipsred" />

            <LinearLayout
                android:id="@+id/lnrheader"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/imalucky"
                android:layout_centerHorizontal="true"
                android:layout_marginLeft="15dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/txtproname"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_horizontal"
                    android:lineSpacingExtra="5dp"
                    android:text=" 10Cr "
                    android:textColor="@color/colordullwhite"
                    android:textSize="14sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/lnrheader"
                android:layout_centerHorizontal="true"
                android:orientation="vertical">


                <ImageView
                    android:id="@+id/imgbuy"
                    android:layout_width="wrap_content"
                    android:layout_height="50dp"
                    android:layout_marginLeft="5dp"
                    android:layout_marginTop="15dp"
                    android:src="@drawable/buychips"
                    android:visibility="gone" />
            </LinearLayout>

        </RelativeLayout>



    </RelativeLayout>
    <!--    </androidx.cardview.widget.CardView>-->
    <TextView
        android:id="@+id/txtAmount"
        android:layout_width="150dp"
        android:layout_height="35dp"
        android:layout_centerInParent="true"
        android:layout_alignParentBottom="true"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/ic_button"
        android:gravity="center"
        android:text="499.00"
        android:textColor="@color/white"
        android:textSize="20dp" />

</RelativeLayout>
