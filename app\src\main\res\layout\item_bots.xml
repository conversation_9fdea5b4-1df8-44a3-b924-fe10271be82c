<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <RelativeLayout
        android:id="@+id/rlt_profile"
        android:layout_width="wrap_content"
        android:layout_marginLeft="@dimen/dp10"
        android:layout_height="wrap_content">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignLeft="@id/img_profile"
            android:layout_alignTop="@id/img_profile"
            android:layout_alignRight="@id/img_profile"
            android:layout_alignBottom="@id/img_profile"
            android:src="@drawable/ic_circle_shin_golden" />

        <de.hdodenhof.circleimageview.CircleImageView
            android:id="@+id/img_profile"
            android:layout_width="@dimen/dp60"
            android:layout_height="@dimen/dp60"
            android:padding="@dimen/dp10"
            android:src="@drawable/app_logo_second" />

    </RelativeLayout>

    <TextView
        android:id="@+id/txt_name"
        style="@style/PlayerWalletTextview"
        android:layout_width="@dimen/dp80"
        android:layout_below="@+id/rlt_profile"
        android:layout_marginLeft="0dp"
        android:maxLength="10"
        android:text="John wick"
        android:singleLine="true"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="10sp" />

        <LinearLayout
            style="@style/user_coin_bg"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/white_lable_small"
            android:orientation="horizontal"
            android:layout_below="@+id/txt_name"
            android:layout_marginTop="-5dp"
            android:backgroundTint="@color/blackbg"
            android:gravity="center_vertical"
            android:visibility="visible">

            <ImageView
                android:layout_height="@dimen/sp17"
                android:layout_width="@dimen/payu_dimen_17dp"
                android:layout_marginLeft="@dimen/dp1"
                android:background="@drawable/rupee_icon"
                />
            <TextView
                android:id="@+id/txt_wallet"
                android:textAlignment="center"
                android:layout_width="match_parent"
                android:textColor="@color/Golder_yellow"
                style="@style/PlayerWalletTextview"
                android:layout_marginLeft="0dp"
                />
        </LinearLayout>

</RelativeLayout>
