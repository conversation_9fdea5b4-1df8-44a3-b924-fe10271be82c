<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:background="@drawable/alert_dialogue_background_3"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingHorizontal="40dp"
        android:paddingTop="20dp"
        android:paddingBottom="20dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:layout_marginBottom="5dp"
            android:fontFamily="@font/oswald"
            android:gravity="center"
            android:text="Choose Players"
            android:textColor="@color/white"
            android:textSize="20dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"


            >

            <LinearLayout
                android:id="@+id/alertdialogueplaylocalbox1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="55dp"
                    android:layout_height="120dp"
                    android:background="@drawable/vs_computer_options_background"
                    android:backgroundTint="@color/red1"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingHorizontal="10dp"
                    android:paddingVertical="15dp">

                    <ImageView
                        android:id="@+id/alertdialogueplaylocalbox1person"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:layout_weight="1"
                        android:adjustViewBounds="true"
                        android:src="@drawable/icon_persion" />

                    <ImageView
                        android:id="@+id/alertdialogueplaylocalbox1computer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:layout_marginTop="10dp"
                        android:layout_weight="1"
                        android:adjustViewBounds="true"
                        android:alpha="0.4"
                        android:src="@drawable/icon_computer"
                        app:tint="@color/white" />


                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/alertdialogueplaylocalbox2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="55dp"
                    android:layout_height="120dp"
                    android:background="@drawable/vs_computer_options_background"
                    android:backgroundTint="@color/yellow1"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingHorizontal="10dp"
                    android:paddingVertical="15dp">

                    <ImageView
                        android:id="@+id/alertdialogueplaylocalbox2person"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:adjustViewBounds="true"
                        android:alpha="0.4"
                        android:src="@drawable/icon_persion" />

                    <ImageView
                        android:id="@+id/alertdialogueplaylocalbox2computer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_weight="1"
                        android:adjustViewBounds="true"
                        android:src="@drawable/icon_computer"
                        app:tint="@color/white" />


                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/alertdialogueplaylocalbox3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="55dp"
                    android:layout_height="120dp"
                    android:background="@drawable/vs_computer_options_background"
                    android:backgroundTint="@color/blue1"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingHorizontal="10dp"
                    android:paddingVertical="15dp">

                    <ImageView
                        android:id="@+id/alertdialogueplaylocalbox3person"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:adjustViewBounds="true"
                        android:alpha="0.4"
                        android:src="@drawable/icon_persion" />

                    <ImageView
                        android:id="@+id/alertdialogueplaylocalbox3computer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_weight="1"
                        android:adjustViewBounds="true"
                        android:src="@drawable/icon_computer"
                        app:tint="@color/white" />


                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/alertdialogueplaylocalbox4"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="55dp"
                    android:layout_height="120dp"
                    android:background="@drawable/vs_computer_options_background"
                    android:backgroundTint="@color/gree1"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingHorizontal="10dp"
                    android:paddingVertical="15dp">

                    <ImageView
                        android:id="@+id/alertdialogueplaylocalbox4person"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:adjustViewBounds="true"
                        android:alpha="0.4"
                        android:src="@drawable/icon_persion" />

                    <ImageView
                        android:id="@+id/alertdialogueplaylocalbox4computer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_weight="1"
                        android:adjustViewBounds="true"
                        android:src="@drawable/icon_computer"
                        app:tint="@color/white" />


                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <Button
            android:id="@+id/vscomputergamemenustartbutton"
            android:layout_width="180dp"
            android:layout_height="@dimen/dimen_50dp"
            android:layout_marginTop="@dimen/dimen_20dp"
            android:adjustViewBounds="true"
            android:layout_marginBottom="@dimen/dimen_20dp"
            android:background="@drawable/button_backgrond"
            android:fontFamily="@font/mama_bear"
            android:scaleType="centerInside"
            android:src="@drawable/start_button"
            android:text="Start"
            android:textColor="@color/white"
            android:textSize="25dp" />
    </LinearLayout>

    <TextView
        android:layout_width="200dp"
        android:layout_height="40dp"
        android:layout_centerHorizontal="true"
        android:background="@drawable/play_local_written"
        android:backgroundTint="@color/yellow3"
        android:elegantTextHeight="true"
        android:fontFamily="@font/oswald"
        android:gravity="center"
        android:text="Vs Computer"
        android:textColor="@color/purple3"
        android:textSize="20dp"
        android:textStyle="bold" />

</RelativeLayout>
