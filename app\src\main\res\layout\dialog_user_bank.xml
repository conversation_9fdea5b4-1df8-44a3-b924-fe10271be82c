<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_height="match_parent"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/home_bg2"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rltDetails"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            style="@style/popUpBoxbgProfile"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignLeft="@id/lnrProfileDetails"
            android:layout_alignTop="@id/lnrProfileDetails"
            android:layout_alignRight="@id/lnrProfileDetails"
            android:layout_alignBottom="@id/lnrProfileDetails" />

        <RelativeLayout
            android:id="@+id/lnrProfileDetails"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:layout_toRightOf="@+id/rlt_profile"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:paddingTop="@dimen/dp10"
            android:paddingBottom="20dp">

            <LinearLayout
                android:id="@+id/img_edit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="@dimen/dp10"
                android:gravity="center">

                <TextView
                    android:id="@+id/tvEditProfile"
                    style="@style/ShadowGoldTextview"
                    android:text="Profile"
                    android:textSize="18sp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tvKYC"
                    style="@style/ShadowGoldTextview"
                    android:layout_marginLeft="@dimen/dp20"
                    android:text="Bank Details"
                    android:textSize="18sp" />

                <ImageView
                    android:id="@+id/verified"
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:layout_marginLeft="3dp"
                    android:visibility="gone" />
            </LinearLayout>


            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/img_edit">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginHorizontal="@dimen/dp20"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/lnr_userinfo"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:orientation="vertical">

                            <LinearLayout style="@style/profileFieldParent">

                                <TextView
                                    android:id="@+id/Headingfiled1"
                                    style="@style/profileFieldTextView"
                                    android:text="Name:" />

                                <TextView
                                    android:id="@+id/txt_diaName"
                                    style="@style/profileFieldValueTextView" />
                            </LinearLayout>

                            <LinearLayout
                                style="@style/profileFieldParent"
                                android:background="@null">

                                <TextView
                                    android:id="@+id/Headingfiled2"
                                    style="@style/profileFieldTextView"
                                    android:text="Mobile no:" />

                                <TextView
                                    android:id="@+id/txt_diaPhone"
                                    style="@style/profileFieldValueTextView" />
                            </LinearLayout>

                            <LinearLayout style="@style/profileFieldParent">

                                <TextView
                                    android:id="@+id/Headingfiled3"
                                    style="@style/profileFieldTextView"
                                    android:text="UPI ID:" />

                                <TextView
                                    android:id="@+id/txt_upi"
                                    style="@style/profileFieldValueTextView" />
                            </LinearLayout>

                            <LinearLayout
                                style="@style/profileFieldParent"
                                android:background="@null"
                                android:visibility="gone">

                                <TextView
                                    android:id="@+id/Headingfiled4"
                                    style="@style/profileFieldTextView"
                                    android:text="Bank Details:" />

                                <TextView
                                    android:id="@+id/txt_bank"
                                    style="@style/profileFieldValueTextView" />
                            </LinearLayout>

                            <LinearLayout
                                style="@style/profileFieldParent"
                                android:visibility="visible">

                                <TextView
                                    android:id="@+id/Headingfiled5"
                                    style="@style/profileFieldTextView"
                                    android:text="Aadhar card no:" />

                                <TextView
                                    android:id="@+id/txt_adhar"
                                    style="@style/profileFieldValueTextView" />
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/lnr_updateuser"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="visible">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="@dimen/dp30"
                                android:paddingLeft="@dimen/dp5"
                                android:paddingBottom="@dimen/dp5"
                                android:text="Bank Name:"
                                android:textAllCaps="false"
                                android:textColor="@color/Golder_yellow" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="170dp"
                                android:layout_marginTop="@dimen/dp5"
                                android:paddingLeft="@dimen/dp5"
                                android:paddingBottom="@dimen/dp5"
                                android:text=" IFSC CODE:"
                                android:textColor="@color/Golder_yellow" />

                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/dp30"
                            android:orientation="horizontal">

                            <EditText
                                android:id="@+id/edt_bank_name"
                                style="@style/EditTextWithBackground_new"
                                android:layout_width="250dp"
                                android:layout_height="@dimen/dp35"
                                android:hint="Enter Bank Name"
                                android:inputType="text" />

                            <EditText
                                android:id="@+id/edt_ifsc"
                                style="@style/EditTextWithBackground_new"
                                android:layout_width="250dp"
                                android:layout_height="@dimen/dp35"
                                android:layout_marginBottom="20dp"
                                android:hint="Enter IFSC CODE" />

                            <Button
                                android:id="@+id/upload_pan"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp7"
                                android:text="Choose Img"
                                android:textAllCaps="true"
                                android:textColor="@color/black"
                                android:visibility="gone" />

                            <RelativeLayout
                                android:id="@+id/rlt_pan"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="@dimen/dp40">

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_alignLeft="@id/img_pan"
                                    android:layout_alignTop="@id/img_pan"
                                    android:layout_alignRight="@id/img_pan"
                                    android:layout_alignBottom="@id/img_pan" />

                                <ImageView
                                    android:id="@+id/img_pan"
                                    android:layout_width="75dp"
                                    android:layout_height="75dp"
                                    android:padding="@dimen/dp10"
                                    android:src="@drawable/app_logo_second"
                                    android:visibility="gone" />

                                <ImageView
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    android:layout_marginLeft="-15dp"
                                    android:layout_marginTop="14dp"
                                    android:layout_toRightOf="@+id/img_pan" />
                            </RelativeLayout>


                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/dp30"
                            android:orientation="horizontal"
                            android:visibility="gone">


                            <Button
                                android:id="@+id/upload_aadhar"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp7"
                                android:text="Choose IMG"
                                android:textColor="@color/black"
                                android:visibility="gone" />

                            <RelativeLayout
                                android:id="@+id/rlt_aadhar"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="@dimen/dp40">

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_alignLeft="@id/img_aadhar"
                                    android:layout_alignTop="@id/img_aadhar"
                                    android:layout_alignRight="@id/img_aadhar"
                                    android:layout_alignBottom="@id/img_aadhar" />

                                <ImageView
                                    android:id="@+id/img_aadhar"
                                    android:layout_width="75dp"
                                    android:layout_height="75dp"
                                    android:padding="@dimen/dp10"
                                    android:src="@drawable/app_logo_second"
                                    android:visibility="gone" />

                                <ImageView
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    android:layout_marginLeft="-15dp"
                                    android:layout_marginTop="14dp"
                                    android:layout_toRightOf="@+id/img_aadhar" />
                            </RelativeLayout>


                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="@dimen/dp30"
                                android:layout_marginTop="@dimen/dp5"
                                android:paddingLeft="@dimen/dp5"
                                android:paddingBottom="@dimen/dp5"
                                android:text="Account Holder Name:"
                                android:textColor="@color/Golder_yellow" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="95dp"
                                android:layout_marginTop="@dimen/dp5"
                                android:paddingLeft="@dimen/dp5"
                                android:paddingBottom="@dimen/dp5"
                                android:text=" Account Number:"
                                android:textColor="@color/Golder_yellow" />
                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/dp30"
                            android:orientation="horizontal">

                            <EditText
                                android:id="@+id/edt_account_holder"
                                style="@style/EditTextWithBackground_new"
                                android:layout_width="250dp"
                                android:layout_height="@dimen/dp35"
                                android:layout_marginBottom="20dp"
                                android:hint="Enter Account Holder Name"
                                android:inputType="text" />

                            <EditText
                                android:id="@+id/edt_account_no"
                                style="@style/EditTextWithBackground_new"
                                android:layout_width="250dp"
                                android:layout_height="@dimen/dp35"
                                android:layout_marginBottom="20dp"
                                android:hint="Enter Account Number"
                                android:inputType="number" />

                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/dp30"
                            android:orientation="horizontal">

                        </LinearLayout>


                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/dp30"
                            android:layout_marginTop="@dimen/dp5"
                            android:paddingLeft="@dimen/dp5"
                            android:text="Passbook Image:"
                            android:visibility="gone"
                            android:textColor="@color/Golder_yellow" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/dp30"
                            android:orientation="horizontal">

                            <Button
                                android:id="@+id/btn_passbook"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp7"
                                android:text="Choose IMG"
                                android:textColor="@color/black"
                                android:visibility="gone" />

                            <RelativeLayout
                                android:id="@+id/rlt_passbook"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="@dimen/dp30"
                                android:paddingBottom="@dimen/dp10">

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_alignLeft="@id/img_passbook"
                                    android:layout_alignTop="@id/img_passbook"
                                    android:layout_alignRight="@id/img_passbook"
                                    android:layout_alignBottom="@id/img_passbook" />

                                <ImageView
                                    android:id="@+id/img_passbook"
                                    android:layout_width="75dp"
                                    android:layout_height="75dp"
                                    android:padding="@dimen/dp10"
                                    android:src="@drawable/app_logo_second"
                                    android:visibility="gone" />

                                <ImageView
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    android:visibility="gone"
                                    android:layout_marginLeft="-15dp"
                                    android:layout_marginTop="14dp"
                                    android:layout_toRightOf="@+id/img_passbook" />
                            </RelativeLayout>


                        </LinearLayout>

                        <TextView
                            android:id="@+id/txt_reason"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingBottom="@dimen/dp5"
                            android:textAlignment="center"
                            android:textColor="@color/Golder_yellow"
                            android:visibility="gone" />

                        <ImageView
                            android:id="@+id/imgsub"
                            android:layout_width="match_parent"
                            android:layout_height="50dp"
                            android:src="@drawable/update" />

                        <EditText
                            android:id="@+id/edtUsername"
                            style="@style/EditTextWithBackground"
                            android:hint="Enter User Name"
                            android:visibility="gone"
                            android:text="NA" />

                        <EditText
                            android:id="@+id/edtUserupi"
                            style="@style/EditTextWithBackground"
                            android:hint="Enter UPI"
                            android:visibility="gone"
                            android:text="NA" />

                        <EditText
                            android:id="@+id/edtUserbank"
                            style="@style/EditTextWithBackground"
                            android:layout_marginBottom="20dp"
                            android:hint="Enter Bank Details"
                            android:visibility="gone"
                            android:text="NA" />

                        <EditText
                            android:id="@+id/edtUseradhar"
                            style="@style/EditTextWithBackground"
                            android:hint="Enter Adahar card"
                            android:visibility="gone"
                            android:text="NA" />
                    </LinearLayout>
                </LinearLayout>
            </ScrollView>

            <ImageView
                android:id="@+id/imgclosetop"
                style="@style/closeButton"
                android:layout_marginTop="0dp"
                android:layout_marginRight="20dp"
                android:visibility="visible" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rlt_profile"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:visibility="gone"
            android:layout_marginTop="20dp">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_alignLeft="@id/img_diaProfile"
                android:layout_alignTop="@id/img_diaProfile"
                android:layout_alignRight="@id/img_diaProfile"
                android:layout_alignBottom="@id/img_diaProfile"
                android:src="@drawable/user_bg_circle" />

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/img_diaProfile"
                android:layout_width="75dp"
                android:layout_height="75dp"
                android:padding="@dimen/dp10"
                android:src="@drawable/app_logo_second" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginLeft="-15dp"
                android:layout_marginTop="14dp"
                android:layout_toRightOf="@+id/img_diaProfile"
                android:src="@drawable/ic_baseline_camera" />
        </RelativeLayout>


    </RelativeLayout>


</RelativeLayout>
