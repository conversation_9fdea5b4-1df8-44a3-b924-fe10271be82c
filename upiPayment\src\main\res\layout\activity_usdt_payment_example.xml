<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@android:color/white">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="USDT Payment System"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp"
        android:textColor="@android:color/black"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Complete USDT Deposit &amp; Withdrawal Solution"
        android:textSize="16sp"
        android:gravity="center"
        android:layout_marginBottom="24dp"
        android:textColor="@android:color/black"/>

    <com.google.android.material.textfield.TextInputLayout
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Amount (USDT)"
        android:layout_marginBottom="16dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_amount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="numberDecimal"
            android:text="10.00"
            android:textColor="@android:color/black" />
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="User ID"
        android:layout_marginBottom="24dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_user_id"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:text="user123"
            android:textColor="@android:color/black" />
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Withdrawal Address (for testing)"
        android:layout_marginBottom="24dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_withdraw_address"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:text="0x742d35Cc6634C0532925a3b8D4C9db96590645d7"
            android:textColor="@android:color/black" />
    </com.google.android.material.textfield.TextInputLayout>

    <Button
        android:id="@+id/btn_deposit"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:text="USDT Deposit (Manual)"
        android:textSize="16sp"
        android:layout_marginBottom="12dp"
        android:backgroundTint="#2E7D32"
        android:textColor="@android:color/white"/>

    <Button
        android:id="@+id/btn_deposit_auto"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:text="USDT Deposit (Auto-Start)"
        android:textSize="16sp"
        android:layout_marginBottom="12dp"
        android:backgroundTint="#1976D2"
        android:textColor="@android:color/white"/>

    <Button
        android:id="@+id/btn_withdraw"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:text="USDT Withdrawal"
        android:textSize="16sp"
        android:layout_marginBottom="24dp"
        android:backgroundTint="#D32F2F"
        android:textColor="@android:color/white"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Features:"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp"
        android:textColor="@android:color/black"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="• Complete USDT payment system\n• QR code generation for deposits\n• Real-time blockchain monitoring\n• Automatic wallet generation\n• Secure withdrawal processing\n• BSC (BEP20) network support"
        android:textSize="14sp"
        android:lineSpacingExtra="4dp"
        android:textColor="@android:color/black"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Deposit: Generate wallet and QR for receiving USDT\nWithdrawal: Send USDT to specified wallet address\nAuto-Start: Automatically opens deposit interface"
        android:textSize="12sp"
        android:layout_marginTop="16dp"
        android:textStyle="italic"
        android:textColor="#666666"/>

</LinearLayout>
