<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="450dp"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardCornerRadius="10dp"
    app:cardElevation="4dp"
    app:cardUseCompatPadding="true"
    android:layout_gravity="bottom|center_horizontal"
    android:gravity="bottom"
    android:background="@drawable/d_top_corner_curve_white"
    >

    <ImageView
        android:id="@+id/img_close"
        android:layout_width="@dimen/dp35"
        android:layout_height="@dimen/dp35"
        android:layout_alignParentEnd="true"
        android:src="@drawable/ic_close_new" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="10dp"
        android:orientation="vertical"
        >

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/select_no_of_players"
            android:textColor="@color/black"
            android:textSize="22sp"
            android:textStyle="bold"
            android:layout_gravity="center"
            android:gravity="center"
            android:fontFamily="cursive"
            />

    <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/ivPlayer5"
                android:layout_width="55dp"
                android:layout_height="55dp"
                android:src="@drawable/avatar"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="10dp"
                android:elevation="10dp"
                />
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@android:color/transparent"
            android:layout_marginTop="-20dp"
            app:cardUseCompatPadding="true"
            app:cardCornerRadius="10dp"
            android:layout_below="@+id/ivPlayer5"
            >



        <LinearLayout
            android:layout_below="@+id/ivPlayer5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:weightSum="2"
            >

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <LinearLayout
                    android:id="@+id/lnr_2player"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:padding="10dp"
                    android:layout_marginTop="-10dp"
                    >

                    <TextView
                        android:id="@+id/tv_2player"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/tow_palyer"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:gravity="center"
                        android:fontFamily="cursive"
                        android:padding="10dp"
                        />

                </LinearLayout>

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">


            <LinearLayout
                android:id="@+id/lnr_5player"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:background="@color/new_colorPrimary"
                android:padding="10dp"
                android:layout_marginTop="-10dp"
                >

                <TextView
                    android:id="@+id/tv_5player"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/five_player"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:fontFamily="cursive"
                    android:padding="10dp"

                    />



            </LinearLayout>


            </RelativeLayout>

        </LinearLayout>

        </androidx.cardview.widget.CardView>
    </RelativeLayout>

        <Button
            android:id="@+id/btn_play"
            android:layout_width="150dp"
            android:layout_height="55dp"
            android:gravity="center"
            android:layout_gravity="center"
            android:text="@string/play_now"
            android:textSize="18sp"
            android:background="@drawable/btn_yellow_discard"
            android:layout_marginTop="30dp"
            />
        <LinearLayout
            android:id="@+id/lnr_private"
            android:gravity="center_horizontal"
            android:layout_width="match_parent"
            android:weightSum="2"
            android:visibility="gone"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_join_private"
                android:layout_width="150dp"
                android:layout_height="55dp"
                android:gravity="center"
                android:layout_gravity="center"
                android:text="Join via Code"
                android:textSize="14sp"
                android:layout_marginBottom="@dimen/dp5"
                android:textStyle="bold"
                android:background="@drawable/btn_yellow_discard"
                android:layout_marginTop="@dimen/dp10"
                />

            <Button
                android:id="@+id/btn_play_private"
                android:layout_width="150dp"
                android:layout_height="55dp"
                android:gravity="center"
                android:visibility="gone"
                android:layout_gravity="center"
                android:text="Private Table"
                android:textSize="14sp"
                android:layout_marginBottom="@dimen/dp5"
                android:textStyle="bold"
                android:background="@drawable/btn_yellow_discard"
                android:layout_marginTop="@dimen/dp10"
                />
        </LinearLayout>


    </LinearLayout>



</RelativeLayout>
