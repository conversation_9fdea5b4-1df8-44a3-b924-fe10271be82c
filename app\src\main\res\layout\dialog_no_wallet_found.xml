<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="300dp"
    android:layout_height="300dp"
    android:background="@drawable/dialog_bg_blank"
    android:padding="20dp"
    android:layout_centerInParent="true"
    android:layout_gravity="center">

    <!-- Close Button -->
    <ImageView
        android:id="@+id/img_close"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:padding="3dp"
        android:src="@drawable/close"
        android:contentDescription="@string/close" />

    <!-- Error Icon -->
    <ImageView
        android:id="@+id/img_error_icon"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_below="@id/img_close"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="10dp"
        android:src="@drawable/close"
        android:background="@drawable/circular"
        android:padding="15dp"
        app:tint="@color/red" />

    <!-- Title -->
    <TextView
        android:id="@+id/txt_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/img_error_icon"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="30dp"
        android:text="❌ No Wallet Found"
        android:textColor="@color/red"
        android:textSize="18sp"
        android:textStyle="bold"
        android:gravity="center" />

    <!-- Message -->
    <TextView
        android:id="@+id/txt_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/txt_title"
        android:layout_marginTop="15dp"
        android:text="Payment services are currently unavailable"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:gravity="center"
        android:alpha="0.9" />

</RelativeLayout>
