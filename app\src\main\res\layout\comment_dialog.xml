<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#80000000"
    android:gravity="bottom"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@drawable/round_bor_top"
        android:orientation="vertical">

        <!--Title-->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/txt_title"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:text="Add New Comment"
                android:textSize="16dp"
                android:visibility="gone" />

            <LinearLayout
                android:id="@+id/ly_close_dialog"
                android:layout_width="50dp"
                android:layout_height="match_parent"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:gravity="center">

                <TextView
                    android:layout_width="23dp"
                    android:layout_height="23dp"
                    android:background="@drawable/ic_close_24"
                    android:backgroundTint="@color/black"
                    android:gravity="center_vertical" />

            </LinearLayout>

        </RelativeLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:orientation="vertical">

            <!--Rating-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="visible">


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:layout_gravity="center_horizontal"
                    android:gravity="center_horizontal"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="Pay to:"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/pay_to"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="Pay to:"
                        android:textColor="@color/black"
                        android:textSize="14sp"
                        android:textStyle="bold" />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:layout_gravity="center_horizontal"
                    android:gravity="center_horizontal"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="14dp"
                        android:layout_height="14dp"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:src="@drawable/green_tick"
                        android:textSize="14sp"
                        android:tint="@color/green" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="@string/account_copied_successfully"
                        android:textColor="@color/black"
                        android:layout_marginStart="2dp"
                        android:textSize="14sp"
                        android:textStyle="normal" />


                </LinearLayout>

            </LinearLayout>

            <!--Add Comment-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="visible">

                <!--Message-->

                <!--Submit Button-->
                <LinearLayout
                    android:id="@+id/ly_submit"
                    android:layout_width="wrap_content"
                    android:layout_height="50dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="12dp"
                    android:background="@drawable/round_bg_yellow">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text="   Open  "
                        android:textColor="@color/white"
                        android:textSize="18sp" />

                    <ImageView
                        android:id="@+id/image"
                        android:layout_width="100dp"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:src="@drawable/ic_payment_new_phonepe"
                        android:textSize="14sp" />
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text="  To Pay"
                        android:textColor="@color/white"
                        android:textSize="18sp" />
                    <TextView
                        android:id="@+id/price"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text=" 000   "
                        android:textStyle="bold"
                        android:textColor="@color/Golder_yellow"
                        android:textSize="18sp" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
