<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_height="wrap_content"
    android:layout_width="match_parent"
    tools:context=".Statement">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:id="@+id/lnr_box"
        android:background="@drawable/d_pop_setting"
        android:layout_below="@+id/rtl_toolbar"
        android:layout_marginTop="@dimen/pop_up_top_margin"
        android:padding="@dimen/dp10"
        >

        <Button
            android:id="@+id/btn_apply"
            android:text="Apply Redeem"
            android:layout_width="160dp"
            android:layout_marginTop="@dimen/dp25"
            android:gravity="center"
            android:textStyle="bold"
            android:visibility="gone"
            android:textSize="@dimen/dimen_17sp"
            android:paddingBottom="@dimen/dp7"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/orange_btn"
            android:layout_height="@dimen/dp50"/>

        <LinearLayout
            android:id="@+id/lnrRuleslist"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="@dimen/dp35"
            android:paddingBottom="@dimen/dp20"
            >

            <include
                android:visibility="gone"
                layout="@layout/view_color_statement_heading"/>

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                >
                <LinearLayout
                    android:id="@+id/lnrlastView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    >
                    <include
                        layout="@layout/view_tour_list"/>
                </LinearLayout>
            </ScrollView>
        </LinearLayout>

    </LinearLayout>


    <include
        layout="@layout/dialog_toolbar"/>


</RelativeLayout>
