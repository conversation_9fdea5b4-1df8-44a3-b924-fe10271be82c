<?xml version="1.1" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="10dp"
    >

    <RelativeLayout
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:padding="5dp"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/d_toolbar_bg"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:paddingTop="8dp"
                android:paddingBottom="8dp"
                android:layout_marginBottom="20dp"
                >

                <TextView
                    android:id="@+id/tv_heading"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Select Payment Type"
                    android:textAllCaps="true"
                    android:textSize="18sp"
                    android:layout_weight="1"
                    app:fontFilePath="@string/Helvetica_Bold_Extra"
                    />

                <ImageView
                    android:id="@+id/ivClose"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_close_new"
                    />

            </LinearLayout>


            <RadioGroup
                android:id="@+id/rg_selectpay"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                >

                <RadioButton
                    android:id="@+id/rbrazor_pay"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Razor Pay"
                    android:textColor="@color/black"
                    android:textSize="22sp"
                    android:textStyle="bold"
                    android:buttonTint="@color/black"
                    android:paddingLeft="10dp"
                    android:layout_marginBottom="10dp"
                    />

                <RadioButton
                    android:id="@+id/rbrazor_cash"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Cash Free"
                    android:textColor="@color/black"
                    android:textSize="22sp"
                    android:textStyle="bold"
                    android:buttonTint="@color/black"
                    android:paddingLeft="10dp"
                    android:layout_marginBottom="10dp"
                    />


            </RadioGroup>


        </LinearLayout>


    </RelativeLayout>

</androidx.cardview.widget.CardView>
