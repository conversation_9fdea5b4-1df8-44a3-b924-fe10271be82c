<?xml version="1.1" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="5dp"
    android:orientation="vertical"
    app:cardBackgroundColor="@color/transparent"
    app:cardCornerRadius="10dp"
    app:cardElevation="10dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="5dp"
        android:layout_gravity="center"
        android:gravity="start|center"
        android:layout_margin="5dp"
        android:background="@color/transparent">

        <ImageView
            android:id="@+id/bell"
            android:layout_width="@dimen/dp20"
            android:layout_height="@dimen/dp20"
            android:layout_marginLeft="10dp"
            android:layout_centerVertical="true"
            android:src="@drawable/bell_img" />

        <TextView
            android:id="@+id/notificationMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@+id/bell"
            android:padding="5dp"
            tools:text="gsfgsfgsfgsfgs"
            android:textColor="@android:color/white"
            android:textSize="15dp"
            android:textStyle="bold" />

    </RelativeLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
