<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp10"
        android:gravity="center"
        >

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignLeft="@+id/lnrRuleslist"
            android:layout_alignTop="@+id/lnrRuleslist"
            android:layout_alignRight="@+id/lnrRuleslist"
            android:layout_alignBottom="@+id/lnrRuleslist"
            android:background="@drawable/d_jackpot_rulebg" />

        <LinearLayout
            android:id="@+id/lnrRuleslist"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="5dp">

                <TextView
                    android:id="@+id/tvTime"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="Time"
                    android:textColor="@color/white"
                    android:textStyle="bold" />

                <View
                    android:layout_width="3dp"
                    android:layout_height="match_parent"
                    android:background="?dividerHorizontal" />

                <TextView
                    android:id="@+id/tvrewards"
                    style="@style/ShadowGoldTextview"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="Rewards" />

                <View
                    android:layout_width="3dp"
                    android:layout_height="match_parent"
                    android:background="?dividerHorizontal" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center">

                    <ImageView
                        android:id="@+id/ivJackpotCard1"
                        android:layout_width="45dp"
                        android:layout_height="45dp"
                        android:src="@drawable/backside_card"
                        />
                    <ImageView
                        android:id="@+id/ivJackpotCard2"
                        android:layout_width="45dp"
                        android:layout_height="45dp"
                        android:src="@drawable/backside_card"
                        android:layout_marginLeft="-25dp"
                        />
                    <ImageView
                        android:id="@+id/ivJackpotCard3"
                        android:layout_width="45dp"
                        android:layout_height="45dp"
                        android:src="@drawable/backside_card"
                        android:layout_marginLeft="-25dp"

                        />


                </LinearLayout>

                <View
                    android:layout_width="3dp"
                    android:layout_height="match_parent"
                    android:background="?dividerHorizontal" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:gravity="center">

                        <de.hdodenhof.circleimageview.CircleImageView
                            android:id="@+id/ivUserImage"
                            android:layout_width="@dimen/dp40"
                            android:layout_height="@dimen/dp40"
                            android:layout_centerHorizontal="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="@dimen/dp10"
                            android:src="@drawable/avatar"
                            android:visibility="visible" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/txtName"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="2dp"
                                android:alpha="0.7"
                                android:ellipsize="end"
                                android:gravity="center"
                                android:shadowColor="@color/black"
                                android:shadowDx="1"
                                android:shadowDy="1"
                                android:shadowRadius="3"
                                android:text="asif"
                                android:textColor="@color/white"
                                android:textSize="13dp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tvbetvalue"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:ellipsize="end"
                                android:shadowColor="@color/black"
                                android:shadowDx="1"
                                android:shadowDy="1"
                                android:shadowRadius="3"
                                android:text="Bet 1000 get 2000"
                                android:textColor="@color/white"
                                android:textSize="13dp"
                                android:textStyle="bold" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>

                <View
                    android:layout_width="3dp"
                    android:layout_height="match_parent"
                    android:background="?dividerHorizontal" />

                <TextView
                    android:id="@+id/tvWinnerId"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="Winner"
                    android:textColor="@color/white"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>
</LinearLayout>
