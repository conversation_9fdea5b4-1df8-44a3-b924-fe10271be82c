<?xml version="1.1" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Static USDT Payment Test"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="20dp"
            android:textColor="@color/black"/>

        <!-- Instructions Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <TextView
                android:id="@+id/tv_instructions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp"
                android:text="Loading instructions..."
                android:textSize="14sp"
                android:textColor="@color/black"/>

        </androidx.cardview.widget.CardView>

        <!-- Input Fields -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Payment Details"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp"
                    android:textColor="@color/black"/>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp">

                    <EditText
                        android:id="@+id/et_amount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Amount (USDT)"
                        android:inputType="numberDecimal"
                        android:textColor="@color/black"/>

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp">

                    <EditText
                        android:id="@+id/et_user_id"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="User ID"
                        android:inputType="text"
                        android:textColor="@color/black"/>

                </com.google.android.material.textfield.TextInputLayout>

                <Button
                    android:id="@+id/btn_start_payment"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Start USDT Payment"
                    android:textSize="16sp"
                    android:padding="12dp"/>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Static Wallet Info -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Static Wallet Information"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp"
                    android:textColor="@color/black"/>

                <TextView
                    android:id="@+id/tv_wallet_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Loading wallet info..."
                    android:textSize="14sp"
                    android:fontFamily="monospace"
                    android:textColor="@color/black"/>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Testing Notes -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="#FFF3E0">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="⚠️ Testing Notes"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp"
                    android:textColor="#E65100"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="• This uses static wallet data for testing\n• QR code will be generated for the static address\n• Payment status will be simulated\n• Check logs with: adb logcat | grep 'USDT_PAYMENT'"
                    android:textSize="14sp"
                    android:textColor="#BF360C"/>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
