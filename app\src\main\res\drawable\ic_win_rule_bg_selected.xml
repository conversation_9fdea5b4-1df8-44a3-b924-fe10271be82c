<layer-list xmlns:android="http://schemas.android.com/apk/res/android" >

    <item>
        <!-- create gradient you want to use with the angle you want to use -->
        <shape android:shape="rectangle" >
            <gradient
                android:angle="0"
                android:centerColor="@android:color/holo_blue_bright"
                android:endColor="@android:color/holo_red_light"
                android:startColor="@android:color/holo_green_light" />

        </shape>
    </item>
    <!-- create the stroke for top, left, bottom and right with the dp you want -->
    <item
        android:bottom="@dimen/dp2"
        android:left="@dimen/dp2"
        android:right="@dimen/dp2"
        android:top="@dimen/dp2">
        <shape android:shape="rectangle" >
            <!-- fill the inside in the color you want (could also be a gradient again if you want to, just change solid to gradient and enter angle, start, maybe center, and end color) -->
            <solid android:color="@color/purple_700" />
        </shape>
    </item>

</layer-list>
