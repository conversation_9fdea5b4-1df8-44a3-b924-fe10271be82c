<?xml version="1.1" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    tools:context="com.first_player_games.OnlineGame.OnlineGame_V2.OnlineGame_V2">

    <ImageView
        android:id="@+id/img1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:adjustViewBounds="true"
        android:scaleType="centerCrop"
        android:src="@drawable/ludo_bg_new"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <EditText
        android:id="@+id/cheatview"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:background="@color/white"
        android:gravity="center"
        android:paddingHorizontal="30dp"
        android:text="6"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@+id/imageButton"
        app:layout_constraintStart_toEndOf="@+id/playernameCotainer"
        app:layout_constraintTop_toTopOf="parent" />


    <LinearLayout
        android:id="@+id/dicecontainer"
        android:layout_width="wrap_content"
        android:layout_height="80dp"
        android:background="@drawable/dice_bg"
        android:clipToPadding="false"
        android:elevation="5dp"
        android:gravity="center"
        android:paddingHorizontal="20dp"
        android:paddingTop="15dp"
        android:paddingBottom="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/red"
            android:layout_width="30dp"
            android:layout_height="match_parent"
            android:layout_marginRight="5dp"
            android:src="@drawable/red_clr" />

        <LinearLayout
            android:id="@+id/playernameCotainer"
            android:layout_width="110dp"
            android:layout_height="wrap_content"
            android:background="@drawable/roundrect"
            android:orientation="vertical">

            <TextView
                android:id="@+id/online_game_playername_red"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/dimen_15sp"
                android:layout_marginTop="@dimen/dimen_15sp"
                android:fontFamily="@font/oswald"
                android:text="PLAYER 1"
                android:textColor="@color/white"
                android:textSize="16dp" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="horizontal"
                android:visibility="gone">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/coin" />

                <TextView
                    android:id="@+id/red_amt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:text="500"
                    android:textColor="@color/white"
                    android:textSize="16dp" />

            </LinearLayout>

        </LinearLayout>


        <ImageView
            android:id="@+id/dice"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:background="@drawable/dice"
            android:elevation="10dp"
            android:src="@drawable/dots_6" />

        <LinearLayout
            android:id="@+id/playernameCotainer2"
            android:layout_width="110dp"
            android:layout_height="wrap_content"
            android:background="@drawable/roundrect"
            android:orientation="vertical">

            <TextView
                android:id="@+id/online_game_playername_blue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_15sp"
                android:layout_marginTop="@dimen/dimen_15sp"
                android:fontFamily="@font/oswald"
                android:gravity="center_horizontal"
                android:text="PLAYER 1"
                android:textColor="@color/white"
                android:textSize="16dp" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="horizontal"
                android:visibility="gone">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/coin" />

                <TextView
                    android:id="@+id/blue_amt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:text="500"
                    android:textColor="@color/white"
                    android:textSize="16dp" />

            </LinearLayout>
        </LinearLayout>

        <ImageView
            android:id="@+id/blue"
            android:layout_width="30dp"
            android:layout_height="match_parent"
            android:layout_marginLeft="5dp"
            android:src="@drawable/blue_clr" />
    </LinearLayout>


    <ImageButton
        android:layout_width="50dp"
        android:layout_height="50dp"

        android:background="@drawable/dice_background_green"
        android:backgroundTint="@color/purple4"
        android:elevation="10dp"
        android:paddingTop="5dp"
        android:paddingRight="5dp"
        android:src="@drawable/chat_icon"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!--    <LinearLayout-->
    <!--        android:id="@+id/playernameCotainer"-->
    <!--        android:layout_width="wrap_content"-->

    <!--        android:layout_height="wrap_content"-->
    <!--        android:background="@drawable/dice_background_red"-->
    <!--        android:backgroundTint="@color/red1"-->
    <!--        android:clipToPadding="false"-->
    <!--        android:elevation="5dp"-->
    <!--        android:gravity="center"-->
    <!--        android:paddingHorizontal="20dp"-->
    <!--        android:paddingTop="10dp"-->
    <!--        android:paddingBottom="15dp"-->
    <!--        app:layout_constraintStart_toStartOf="parent"-->
    <!--        app:layout_constraintTop_toTopOf="parent">-->

    <!--        <TextView-->
    <!--            android:id="@+id/playernameview"-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:fontFamily="@font/oswald"-->
    <!--            android:text="Gagan Sroay's Turn"-->
    <!--            android:textColor="@color/white"-->
    <!--            android:textSize="20dp" />-->
    <!--    </LinearLayout>-->

    <TextView
        android:id="@+id/timeview"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:layout_marginRight="30dp"
        android:fontFamily="@font/mama_bear"
        android:text="30"
        android:textColor="@color/white"
        android:textSize="20dp"
        app:layout_constraintEnd_toStartOf="@+id/imageButton"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageButton
        android:id="@+id/imageButton"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_margin="10dp"
        android:background="@drawable/dice"
        android:backgroundTint="@color/purple4"
        android:src="@drawable/icon_hamburger_menu"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.first_player_games.OnlineGame.OnlineGame_V2.DotsView
        android:id="@+id/ludodotview"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:elevation="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1:1.1"
        app:layout_constraintTop_toTopOf="parent" />

    <!--inside image-->
    <ImageView
        android:id="@+id/ludoboardimageview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:src="@drawable/new_board_design"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <RelativeLayout
        android:id="@+id/rltGameReconnect"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:gravity="center_vertical"
        android:background="@drawable/bgreconnect"
        android:visibility="gone"
        tools:ignore="MissingConstraints"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:id="@+id/imgGameReconnect"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:src="@drawable/reconnect" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/linearLayout3"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:clipToPadding="false"
        android:visibility="gone"
        android:paddingTop="10dp"
        android:translationY="1dp"
        app:layout_constraintBottom_toTopOf="@+id/ludoboardimageview">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="0.3"
            android:visibility="gone"
            android:background="@drawable/one_side_round_red"
            android:elevation="5dp"
            android:gravity="center_vertical"
            android:paddingLeft="5dp">

            <TextView
                android:id="@+id/online_game_playername_red"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:alpha="0.8"
                android:fontFamily="@font/oswald"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/white"
                android:textSize="20dp"
                android:textStyle="bold"

                tools:ignore="DuplicateIds" />

            <ImageView
                android:id="@+id/dice_red"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_marginRight="35dp"
                android:background="@drawable/dice_small"
                android:elevation="10dp"
                android:src="@drawable/dots_6" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="0.4" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="0.3"
            android:background="@drawable/one_side_round_yellow"
            android:elevation="5dp"
            android:gravity="center_vertical"
            android:paddingRight="5dp">

            <ImageView
                android:id="@+id/dice_yellow"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_marginLeft="35dp"
                android:background="@drawable/dice_small"
                android:elevation="10dp"
                android:src="@drawable/dots_6" />

            <TextView
                android:id="@+id/online_game_playername_yellow"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"

                android:alpha="0.8"
                android:fontFamily="@font/oswald"
                android:gravity="right"
                android:paddingRight="2dp"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/white"
                android:textSize="20dp"
                android:textStyle="bold" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/linearLayout4"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:clipToPadding="false"
        android:paddingBottom="10dp"
        android:translationY="-1dp"
        app:layout_constraintTop_toBottomOf="@+id/ludoboardimageview">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="0.3"
            android:background="@drawable/one_side_round_green"
            android:elevation="5dp"
            android:visibility="gone"
            android:gravity="center_vertical"
            android:paddingLeft="5dp">

            <TextView
                android:id="@+id/online_game_playername_green"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:alpha="0.8"
                android:fontFamily="@font/oswald"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/white"
                android:textSize="20dp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/dice_green"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_marginRight="35dp"
                android:background="@drawable/dice_small"
                android:elevation="10dp"
                android:src="@drawable/dots_6" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="0.4" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="0.3"
            android:background="@drawable/one_side_round_blue"
            android:elevation="5dp"
            android:visibility="gone"
            android:gravity="center_vertical"
            android:paddingRight="5dp">

            <ImageView
                android:id="@+id/dice_blue"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_marginLeft="35dp"
                android:background="@drawable/dice_small"
                android:elevation="10dp"
                android:src="@drawable/dots_6" />

            <TextView
                android:id="@+id/online_game_playername_blue"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:alpha="0.8"
                android:fontFamily="@font/oswald"
                android:gravity="right"
                android:paddingRight="2dp"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/white"
                android:textSize="20dp"
                android:textStyle="bold" />

        </LinearLayout>
    </LinearLayout>

    <ImageView
        android:id="@+id/arrow_view"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:src="@drawable/arrow_down"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/linearLayout4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:tint="@color/yellow3" />

</androidx.constraintlayout.widget.ConstraintLayout>
