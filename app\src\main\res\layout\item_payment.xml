<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:layout_marginBottom="10dp"
    android:background="@color/white">

    <LinearLayout
        android:id="@+id/lyPhonePay"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginBottom="15dp"
        android:background="@drawable/d_background_border_gray"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="12dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.55"
                android:gravity="center_vertical">

                <ImageView
                    android:id="@+id/image"
                    android:layout_width="130dp"
                    android:layout_height="30dp"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_payment_phonepe" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.35"
                android:gravity="center|end">

                <TextView
                    android:id="@+id/tvMobile"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:visibility="gone"
                    android:textColor="@color/text_color_primary"
                    android:textSize="@dimen/pay_sub_topic" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.1"
                android:gravity="center|end">

                <TextView
                    android:layout_width="18dp"
                    android:layout_height="22dp"
                    android:background="@drawable/right_arrow" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
