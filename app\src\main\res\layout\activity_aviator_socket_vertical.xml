<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/aviator_black_bg"
    android:paddingTop="20dp"
    tools:context="._Aviator.Aviator_Game_Activity">

    <!-- Previous gif_Count values display -->
    <LinearLayout
        android:id="@+id/lnr_previous_counts"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:background="#33000000"
        android:padding="5dp">

        <TextView
            android:id="@+id/txt_prev_count1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginEnd="10dp"
            android:shadowColor="@color/black"
            android:shadowDx="0.0"
            android:shadowDy="0.0"
            android:shadowRadius="5"
            android:text="" />

        <TextView
            android:id="@+id/txt_prev_count2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginEnd="10dp"
            android:shadowColor="@color/black"
            android:shadowDx="0.0"
            android:shadowDy="0.0"
            android:shadowRadius="5"
            android:text="" />

        <TextView
            android:id="@+id/txt_prev_count3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginEnd="10dp"
            android:shadowColor="@color/black"
            android:shadowDx="0.0"
            android:shadowDy="0.0"
            android:shadowRadius="5"
            android:text="" />

        <TextView
            android:id="@+id/txt_prev_count4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginEnd="10dp"
            android:shadowColor="@color/black"
            android:shadowDx="0.0"
            android:shadowDy="0.0"
            android:shadowRadius="5"
            android:text="" />

        <TextView
            android:id="@+id/txt_prev_count5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:shadowColor="@color/black"
            android:shadowDx="0.0"
            android:shadowDy="0.0"
            android:shadowRadius="5"
            android:text="" />
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/lnr_gif"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/lnr_previous_counts">

        <!-- Custom Animation View -->

        <!-- GIF ImageView for animations (fallback/hidden) -->
        <com.gamegards.luckyrbm._Aviator.AviatorAnimationView
            android:id="@+id/aviator_animation_view"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_350dp"
            android:layout_marginBottom="-50dp"
            android:visibility="visible" />

        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/gif_roket"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_350dp"
            android:layout_marginLeft="-15dp"
            android:layout_marginBottom="-50dp"
            android:background="@drawable/aviator_graph_bg"
            android:src="@drawable/rocket_stop"
            android:visibility="gone" />

        <!-- Text overlay for rocket value -->
        <TextView
            android:id="@+id/txt_rocket_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="50dp"
            android:textColor="@color/white"
            android:textSize="56sp"
            android:textStyle="bold"
            android:shadowColor="@color/black"
            android:shadowDx="2.0"
            android:shadowDy="2.0"
            android:shadowRadius="8"
            android:visibility="gone" />

    </RelativeLayout>

    <ImageView
        android:id="@+id/imgback"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_marginLeft="@dimen/dp20"
        android:layout_marginTop="@dimen/dp10"
        android:src="@drawable/back"
        android:visibility="visible"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <LinearLayout
        android:id="@+id/lnr_userdetails"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:layout_alignParentRight="true"
        android:orientation="horizontal">

        <de.hdodenhof.circleimageview.CircleImageView
            android:id="@+id/imaprofile"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:visibility="gone"
            android:layout_centerVertical="true"
            android:padding="1dp"
            android:src="@drawable/pro_img" />

        <TextView
            android:id="@+id/txt_username"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:gravity="center"
            android:visibility="gone"
            android:textColor="@color/white"
            android:textStyle="bold"
            android:layout_marginRight="20dp"
            android:textSize="17sp"/>

        <RelativeLayout
            android:layout_width="@dimen/dp140"
            android:layout_height="@dimen/dp35"
            android:layout_gravity="center"
            android:gravity="center"
            android:background="@drawable/player_wallet"
            android:orientation="horizontal">


            <ImageView
                android:id="@+id/imgicon"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:src="@drawable/money" />

            <TextView
                android:id="@+id/txt_wallet_amt"
                android:layout_width="@dimen/dp120"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:layout_toRightOf="@+id/imgicon"
                android:gravity="center"
                android:minWidth="80dp"
                android:text="2000"
                android:textColor="@color/yellow"
                android:textSize="16sp" />

            <ImageView
                android:id="@+id/iv_add"
                android:layout_width="25dp"
                android:layout_height="30dp"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:layout_toRightOf="@+id/txt_wallet_amt"
                android:src="@drawable/add_button"
                android:visibility="gone" />

        </RelativeLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/lnr_pay"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp120"
        android:layout_below="@+id/lnr_gif"
        android:layout_alignParentEnd="true"
        android:layout_marginStart="@dimen/dp5"
        android:layout_marginTop="-15dp"
        android:layout_marginEnd="@dimen/dp5"
        android:layout_marginBottom="@dimen/dp5"
        android:background="@drawable/gray_rounded_corner"
        android:gravity="center"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp10"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@drawable/histoy_pannel"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/btn_pluse"
                    android:layout_width="40dp"
                    android:layout_height="30dp"
                    android:onClick="pluseValue"
                    android:src="@drawable/plus_icon" />

                <TextView
                    android:id="@+id/txt_inner_timer"
                    android:layout_width="40dp"
                    android:layout_height="30dp"
                    android:gravity="center"
                    android:text="1"
                    android:textSize="15dp"
                    android:visibility="gone" />

                <EditText
                    android:id="@+id/txt_amount"
                    android:layout_width="55dp"
                    android:layout_height="@dimen/dp35"
                    android:layout_gravity="center"
                    android:background="@null"
                    android:gravity="center"
                    android:inputType="numberDecimal|number"
                    android:maxLines="1"
                    android:enabled="true"
                    android:paddingBottom="4dp"
                    android:text="10"
                    android:textColor="@color/yellow"
                    android:textSize="17dp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/btn_minus"
                    android:layout_width="40dp"
                    android:layout_height="30dp"
                    android:onClick="minusValue"
                    android:src="@drawable/minus_icon" />
            </LinearLayout>

            <!-- First row of bet buttons -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/txt_bet1"
                    android:layout_width="@dimen/dp80"
                    android:layout_height="@dimen/dp25"
                    android:layout_gravity="center"
                    android:background="@drawable/bet_selected"
                    android:gravity="center"
                    android:minWidth="80dp"
                    android:paddingTop="@dimen/dp3"
                    android:paddingBottom="@dimen/dp3"
                    android:text="100"
                    android:textColor="@color/white"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/txt_bet2"
                    android:layout_width="@dimen/dp80"
                    android:layout_height="@dimen/dp25"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/dp10"
                    android:background="@drawable/bet_selected"
                    android:gravity="center"
                    android:minWidth="80dp"
                    android:paddingTop="@dimen/dp3"
                    android:paddingBottom="@dimen/dp3"
                    android:text="200"
                    android:textColor="@color/white"
                    android:textSize="15sp" />
            </LinearLayout>

            <!-- Second row of bet buttons -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/txt_bet3"
                    android:layout_width="@dimen/dp80"
                    android:layout_height="@dimen/dp25"
                    android:layout_gravity="center"
                    android:background="@drawable/bet_selected"
                    android:gravity="center"
                    android:minWidth="80dp"
                    android:paddingTop="@dimen/dp3"
                    android:paddingBottom="@dimen/dp3"
                    android:text="500"
                    android:textColor="@color/white"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/txt_bet4"
                    android:layout_width="@dimen/dp80"
                    android:layout_height="@dimen/dp25"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/dp10"
                    android:background="@drawable/bet_selected"
                    android:gravity="center"
                    android:minWidth="80dp"
                    android:paddingTop="@dimen/dp3"
                    android:paddingBottom="@dimen/dp3"
                    android:text="1000"
                    android:textColor="@color/white"
                    android:textSize="15sp" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="@dimen/dp10"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_into_amount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:text="0"
                    android:textSize="20dp"
                    android:visibility="invisible" />

                <TextView
                    android:id="@+id/tv_waiting"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:text="Waiting For Next Round"
                    android:textColor="@color/white"
                    android:textSize="13sp"
                    android:visibility="invisible" />
            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp70"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/btn_start"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/dp5"
                    android:background="@drawable/aviator_bet_button"
                    android:gravity="center"
                    android:text="Bet"
                    android:textColor="@color/white"
                    android:textSize="20dp" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/lnr_bethistory"
        android:layout_width="@dimen/payu_dimen_200dp"
        android:layout_height="@dimen/dp35"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dp10"
        android:layout_below="@+id/lnr_pay"
        android:background="@drawable/bet_history_bg">

        <TextView
            android:id="@+id/tv_allBets"
            android:layout_width="@dimen/dp100"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/bet_selected"
            android:gravity="center"
            android:text="All Bets"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_myBets"
            android:layout_width="@dimen/dp100"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:text="My Bets"
            android:textColor="@color/white"
            android:textSize="15sp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:layout_below="@id/lnr_bethistory"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp30"
            android:background="@drawable/players_pannels"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:elevation="2dp"
            >

            <LinearLayout
                android:id="@+id/lnrSerial"
                android:layout_width="0dp"
                android:layout_marginStart="@dimen/dp10"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                >

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:text="Name"
                    android:textColor="@color/white"
                    android:gravity="center"
                    />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/lnrDate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                >

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:text="Amount"
                    android:textColor="@color/white"
                    android:gravity="center"
                    />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/lnrGame"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:visibility="visible"
                android:layout_weight="0.5"
                >

                <TextView
                    android:id="@+id/tvGame"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:text=""
                    android:textColor="@color/white"
                    android:gravity="center"
                    android:textStyle="bold"

                    />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/lnrCash"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.1"
                >

                <TextView
                    android:id="@+id/tvCash"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:text="CashOut"
                    android:textColor="@color/white"
                    android:gravity="center"
                    />

            </LinearLayout>

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rec_winning"
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/dp3"
            tools:listitem="@layout/vertical_winnig_itemview"
            android:layout_height="match_parent"
            />


    </LinearLayout>

    <TextView
        android:id="@+id/txt_second"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="70dp"
        android:text="1.00x"
        android:textColor="@color/white"
        android:textSize="18dp"
        android:textStyle="bold"
        android:visibility="invisible" />

    <TextView
        android:id="@+id/txt_start"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/txt_second"
        android:layout_centerHorizontal="true"
        android:text="Starts In"
        android:textColor="@color/white"
        android:textSize="18dp"
        android:visibility="gone" />


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="60dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_gravity="center"
            android:gravity="center">

            <TextView
                android:id="@+id/txt"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_gravity="center"
                android:gravity="center"
                android:shadowColor="@color/black"
                android:shadowDx="0.0"
                android:shadowDy="0.0"
                android:shadowRadius="18"
                android:text="Next game starts in "
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                android:visibility="gone" />

            <TextView
                android:id="@+id/txt_timer"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center"
                android:background="@drawable/clock"
                android:gravity="center"
                android:paddingLeft="2dp"
                android:shadowColor="@color/black"
                android:shadowDx="0.0"
                android:shadowDy="0.0"
                android:shadowRadius="18"
                android:text="5"
                android:textColor="@color/design_default_color_secondary_variant"
                android:textSize="16dp"
                android:visibility="gone" />

        </LinearLayout>

        <TextView
            android:id="@+id/txt_exit"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_gravity="center"
            android:gravity="center"
            android:shadowColor="@color/black"
            android:shadowDx="0.0"
            android:shadowDy="0.0"
            android:shadowRadius="18"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            android:visibility="gone" />

        <TextView
            android:id="@+id/txt_exit_value"
            android:layout_width="wrap_content"
            android:layout_height="60dp"
            android:layout_gravity="center"
            android:gravity="center"
            android:shadowColor="@color/black"
            android:shadowDx="0.0"
            android:shadowDy="0.0"
            android:shadowRadius="18"
            android:textColor="@color/white"
            android:textSize="40sp"
            android:textStyle="bold"
            android:visibility="gone" />

        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/img_start_game"
            android:layout_width="8150dp"
            android:layout_height="110dp"
            android:layout_gravity="center"
            android:src="@drawable/waiting_for_next_round"
            android:visibility="gone" />

    </LinearLayout>

</RelativeLayout>
