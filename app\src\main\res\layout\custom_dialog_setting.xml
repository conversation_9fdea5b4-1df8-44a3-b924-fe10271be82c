<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#00000000">

    <RelativeLayout
        style="@style/dialogParentStyle">


        <RelativeLayout
            android:layout_width="500dp"
            android:layout_height="200dp"
            style="@style/popUpBoxbg"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:visibility="gone"
            >

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="40dp"
                android:layout_marginVertical="45dp"
                >
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_centerInParent="true"
            >

            <ImageView
                android:id="@+id/imgclose"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:visibility="gone"
                android:src="@drawable/ignoregame"/>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginLeft="5dp"
                android:layout_marginTop="5dp"
                android:layout_marginRight="5dp"
                android:background="@drawable/pop_list">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=" Disable All "
                    android:textColor="@color/colordullwhite"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:textSize="15dp"
                    android:layout_alignParentLeft="true"
                    />

                <Switch
                    android:id="@+id/switch1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:text="" />

            </RelativeLayout>


<!--            <RelativeLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="40dp"-->
<!--                android:layout_marginLeft="5dp"-->
<!--                android:layout_marginTop="5dp"-->
<!--                android:layout_marginRight="5dp"-->
<!--                android:background="@drawable/pop_list">-->

<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text=" Mute Voice "-->
<!--                    android:textColor="@color/colordullwhite"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:layout_marginLeft="15dp"-->
<!--                    android:textSize="15dp"-->
<!--                    android:layout_alignParentLeft="true"-->
<!--                    />-->

<!--                <Switch-->
<!--                    android:id="@+id/switch2"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_alignParentRight="true"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:layout_marginRight="15dp"-->
<!--                    android:text="" />-->

<!--            </RelativeLayout>-->


<!--            <RelativeLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="40dp"-->
<!--                android:layout_marginLeft="5dp"-->
<!--                android:layout_marginTop="5dp"-->
<!--                android:layout_marginRight="5dp"-->
<!--                android:background="@drawable/pop_list">-->

<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text=" Gift Sounds "-->
<!--                    android:textColor="@color/colordullwhite"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:layout_marginLeft="15dp"-->
<!--                    android:textSize="15dp"-->
<!--                    android:layout_alignParentLeft="true"-->
<!--                    />-->

<!--                <Switch-->
<!--                    android:id="@+id/switch3"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:layout_alignParentRight="true"-->
<!--                    android:layout_marginRight="15dp"-->
<!--                    android:text="" />-->

<!--            </RelativeLayout>-->


<!--            <RelativeLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="40dp"-->
<!--                android:layout_marginLeft="5dp"-->
<!--                android:layout_marginTop="5dp"-->
<!--                android:layout_marginRight="5dp"-->
<!--                android:background="@drawable/pop_list">-->

<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text=" Game Request Sound "-->
<!--                    android:textColor="@color/colordullwhite"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:layout_marginLeft="15dp"-->
<!--                    android:textSize="15dp"-->
<!--                    android:layout_alignParentLeft="true"-->
<!--                    />-->

<!--                <Switch-->
<!--                    android:id="@+id/switch4"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_alignParentRight="true"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:layout_marginRight="15dp"-->
<!--                    android:text="" />-->

<!--            </RelativeLayout>-->


<!--            <RelativeLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="40dp"-->
<!--                android:layout_marginLeft="5dp"-->
<!--                android:layout_marginTop="5dp"-->
<!--                android:layout_marginRight="5dp"-->
<!--                android:background="@drawable/pop_list">-->

<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text=" Effects "-->
<!--                    android:textColor="@color/colordullwhite"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:layout_marginLeft="15dp"-->
<!--                    android:textSize="15dp"-->
<!--                    android:layout_alignParentLeft="true"-->
<!--                    />-->

<!--                <Switch-->
<!--                    android:id="@+id/switch5"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:layout_marginRight="15dp"-->
<!--                    android:layout_alignParentRight="true"-->
<!--                    android:text="" />-->

<!--            </RelativeLayout>-->


<!--            <RelativeLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="40dp"-->
<!--                android:layout_marginLeft="5dp"-->
<!--                android:layout_marginTop="5dp"-->
<!--                android:layout_marginRight="5dp"-->
<!--                android:background="@drawable/pop_list">-->

<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text=" Player Turn Sound "-->
<!--                    android:textColor="@color/colordullwhite"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:layout_marginLeft="15dp"-->
<!--                    android:textSize="15dp"-->
<!--                    android:layout_alignParentLeft="true"-->
<!--                    />-->

<!--                <Switch-->
<!--                    android:id="@+id/switch6"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_alignParentRight="true"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:layout_marginRight="15dp"-->
<!--                    android:text="" />-->

<!--            </RelativeLayout>-->



<!--            <RelativeLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="40dp"-->
<!--                android:layout_marginLeft="5dp"-->
<!--                android:layout_marginTop="5dp"-->
<!--                android:layout_marginRight="5dp"-->
<!--                android:background="@drawable/pop_list">-->

<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text=" Time Over Notification "-->
<!--                    android:textColor="@color/colordullwhite"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:layout_marginLeft="15dp"-->
<!--                    android:textSize="15dp"-->
<!--                    android:layout_alignParentLeft="true"-->
<!--                    />-->

<!--                <Switch-->
<!--                    android:id="@+id/switch7"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_alignParentRight="true"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:layout_marginRight="15dp"-->
<!--                    android:text="" />-->

<!--            </RelativeLayout>-->

<!--            <RelativeLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="40dp"-->
<!--                android:layout_marginLeft="5dp"-->
<!--                android:layout_marginTop="5dp"-->
<!--                android:layout_marginRight="5dp"-->
<!--                android:background="@drawable/pop_list">-->

<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text=" Vibrate "-->
<!--                    android:textColor="@color/colordullwhite"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:layout_marginLeft="15dp"-->
<!--                    android:textSize="15dp"-->
<!--                    android:layout_alignParentLeft="true"-->
<!--                    />-->

<!--                <Switch-->
<!--                    android:id="@+id/switch8"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_alignParentRight="true"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:layout_marginRight="15dp"-->
<!--                    android:text="" />-->

<!--            </RelativeLayout>-->


        </LinearLayout>
            </ScrollView>
    </RelativeLayout>

    <ImageView
        android:id="@+id/imgclosetop"
        android:layout_alignParentRight="true"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:src="@drawable/ic_close_new"
        android:layout_marginTop="10dp"
        android:layout_marginRight="10dp"
        android:visibility="visible"
        />

        <LinearLayout
            android:id="@+id/lnrVolume"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_centerInParent="true"
            android:clickable="true"
            >

            <ImageView
                android:id="@+id/ivVolume"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:src="@drawable/ic_silent"
                android:layout_marginBottom="20dp"
                android:layout_gravity="center_horizontal"
                />

            <TextView
                android:id="@+id/tvVolume"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Game Valume off"
                android:textSize="22sp"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:fontFamily="cursive"
                />

        </LinearLayout>

    </RelativeLayout>

</RelativeLayout>
