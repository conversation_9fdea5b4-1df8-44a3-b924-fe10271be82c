<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    >

    <View
        android:layout_below="@+id/tvPoints"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/gray_bg"
        />

    <TextView
        android:id="@+id/tvPoints"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:padding="5dp"
        android:text="@string/points"
        android:textColor="@color/black"

        />

</RelativeLayout>
