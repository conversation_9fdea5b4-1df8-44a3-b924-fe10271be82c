<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="8dp"
    >

    <RelativeLayout
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        >


        <ImageView
            android:id="@+id/img_profile"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentEnd="true"
            android:src="@drawable/gamechaticon"
            android:background="@drawable/circle"
            />


        <TextView
            android:id="@+id/txtMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="5dp"
            android:text="hello"
            android:padding="8dp"
            android:textColor="@android:color/white"
            android:background="@drawable/background_right"
            android:layout_toLeftOf="@+id/img_profile"
            />

    </RelativeLayout>





</RelativeLayout>
