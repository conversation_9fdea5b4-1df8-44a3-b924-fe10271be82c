<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/bottom_sheet"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#1A000000"
    android:orientation="vertical"
    app:behavior_hideable="false"
    app:behavior_peekHeight="0dp"
    app:layout_behavior="@string/bottom_sheet_behavior"
    android:clickable="true">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="250dp"
        android:orientation="vertical"
        android:background="@drawable/imglogin_box"
        android:layout_alignParentBottom="true"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:text="Enter Reffer Code "
            android:textColor="@color/white"
            android:textStyle="bold"
            android:textSize="18sp"
            />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                >

                <EditText
                    android:id="@+id/edt_reffer"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/img_loginedt_box"
                    android:hint="Enter Referal Code (Optional)"
                    android:inputType="text"
                    android:paddingLeft="25dp"
                    android:textColor="@color/white"
                    android:textColorHint="@color/white"
                    android:focusable="false"
                    />

                <ImageView
                    android:layout_width="34dp"
                    android:layout_height="34dp"
                    android:src="@drawable/img_referalcode"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="5dp"
                    />


            </RelativeLayout>

            <RadioGroup
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:id="@+id/bottomradioGroup"
                android:orientation="horizontal"
                android:layout_gravity="center"
                android:gravity="center"
                android:layout_marginTop="10dp"
                >

                <RadioButton
                    android:id="@+id/radioMale"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Male"
                    android:layout_gravity="center_vertical"
                    android:checked="true"
                    android:textSize="20dp"
                    android:gravity="center"
                    android:layout_marginRight="30dp"
                    android:buttonTint="@color/white"

                    />

                <RadioButton
                    android:id="@+id/radioFemale"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Female"
                    android:checked="false"
                    android:layout_gravity="end|center_vertical"
                    android:textSize="20dp"
                    android:gravity="center"
                    android:layout_marginLeft="30dp"
                    android:buttonTint="@color/white"
                    />
            </RadioGroup>

        </LinearLayout>

        <Button
            android:id="@+id/btnrefer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_alignParentBottom="true"
            android:text="Submit"
            android:textSize="17sp"
            android:background="@color/lghtAccent"
            android:textColor="@color/white"
            />


    </RelativeLayout>


</RelativeLayout>
