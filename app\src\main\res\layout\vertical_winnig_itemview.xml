<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp30"
    android:layout_marginBottom="3dp"
    app:cardUseCompatPadding="true"
    app:cardElevation="1dp"
    app:cardCornerRadius="10dp"
    android:background="@drawable/all_players_pannel"
    android:clickable="true"
    android:foreground="?selectableItemBackground"
    android:focusable="true"
    >

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_centerVertical="true"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/lnr_parent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp3"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:elevation="2dp">

                <LinearLayout
                    android:id="@+id/lnrDate"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1">

                    <TextView
                        android:id="@+id/tvName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:text="Name"
                        android:textColor="@color/white" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/lnrAmount"
                    android:layout_width="0dp"
                    android:visibility="visible"
                    android:layout_height="wrap_content"
                    android:layout_weight="1">

                    <TextView
                        android:id="@+id/tvAmount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:text="3 patti"
                        android:textColor="@color/white" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/lnrbet"
                    android:layout_width="0dp"
                    android:visibility="visible"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5">

                    <TextView
                        android:id="@+id/tvBet"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:layout_gravity="center"
                        android:layout_marginTop="2dp"
                        android:textStyle="bold"
                        android:layout_marginBottom="@dimen/dp2"
                        android:textSize="12sp"
                        android:background="@drawable/bet_selected"
                        android:text="1.0X"
                        android:textColor="@color/white" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/lnrCash"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.1">

                    <TextView
                        android:id="@+id/tvCash"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:text="0"
                        android:textColor="@color/green" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="16dp"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:id="@+id/txtid"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Table Id : "
                    android:textColor="@android:color/holo_blue_dark"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/txtusername"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="casual"
                    android:text="User Name"
                    android:textColor="@android:color/white"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/bulkchipsred" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="6dp"
                        android:fontFamily="monospace"
                        android:text="Ammout : "
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:textStyle="bold" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <ImageView
            android:id="@+id/imgreward"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:src="@drawable/reward"
            android:layout_centerVertical="true"
            android:visibility="gone"
            android:layout_alignParentRight="true"
            android:layout_marginRight="10dp" />
    </RelativeLayout>

</RelativeLayout>
