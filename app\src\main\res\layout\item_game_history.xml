<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginBottom="10dp">

    <LinearLayout
        android:id="@+id/lnr_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/blackbg"
        >


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            >

            <LinearLayout
                android:id="@+id/lnrSerialno"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.3"
                android:visibility="gone"
                >

                <TextView
                    android:id="@+id/tvSerialnumber"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:text="@string/serial_no"
                    android:textColor="@color/white"
                    android:gravity="center"
                    />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.5"
                >

                <TextView
                    android:id="@+id/tv_you"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:background="@drawable/circle_cancel"
                    android:backgroundTint="@color/BrownColor"
                    android:text="@string/you"
                    android:textSize="9sp"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:visibility="gone"
                    />

                <TextView
                    android:id="@+id/tv_rank"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:text="Rank"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:singleLine="true"
                    android:layout_marginLeft="10dp"
                    android:layout_gravity="center"
                    android:gravity="center"
                    />


            </LinearLayout>

            <LinearLayout
                android:id="@+id/lnrWinnerStatus"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                >

                <TextView
                    android:id="@+id/tv_playername"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    tools:text="Player"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:visibility="visible"
                    android:layout_gravity="center"
                    android:gravity="center"
                    />


            </LinearLayout>

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3">

                <LinearLayout
                    android:id="@+id/lnr_dropped"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:layout_weight="3"
                    >

                    <TextView
                        android:id="@+id/txtresult"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:text="Dropped"
                        android:textColor="@color/white"
                        android:gravity="center"
                        />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/lnrCardsList"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center">

                    <HorizontalScrollView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:scrollbars="none">

                        <LinearLayout
                            android:id="@+id/lnr_declareview"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginTop="2dp"
                            android:gravity="center"
                            android:orientation="horizontal" />
                    </HorizontalScrollView>
                </LinearLayout>

            </RelativeLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.5"
                >

                <TextView
                    android:id="@+id/tv_score"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:text="0"
                    android:textColor="@color/white"
                    android:gravity="center"
                    />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/lnrTotal"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:visibility="visible"
                >

                <TextView
                    android:id="@+id/tvAmount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:text="0"
                    android:textColor="@color/white"
                    android:gravity="center"
                    />

            </LinearLayout>


        </LinearLayout>

        <View
            android:layout_below="@+id/lnr_declareview"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@drawable/help_divider"
            android:layout_marginTop="5dp"
            />

    </LinearLayout>

</RelativeLayout>
