<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="50dp"
        app:cardCornerRadius="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#222222"
                android:padding="5dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:text="Result | Game ID : "
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    android:textStyle="bold" />
                <TextView
                    android:id="@+id/txt_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:text=""
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/imgclosetop"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_gravity="center_vertical"
                    android:elevation="2dp"
                    android:src="@drawable/close"
                    android:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#3F3F3F"
                android:elevation="2dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.8">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:text="Username"
                        android:textColor="@color/white" />
                </LinearLayout>


                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:text="Cards"
                        android:textColor="@color/white" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/lnr_box"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginTop="-10dp"
                android:layout_marginBottom="-10dp"
                android:background="@drawable/box_winbg"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="10dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:background="@color/white"
                    android:orientation="vertical"
                    android:paddingBottom="10dp">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recUsercardlist"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#035C08"
                android:elevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_weight="1">

                    <TextView
                        android:id="@+id/txt_timer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:text="Get Ready - Next game start in 0 second(s)"
                        android:textColor="@color/white" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="20dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:text="Joker"
                        android:textColor="@color/white"
                        android:textSize="15sp" />

                    <ImageView
                        android:id="@+id/imgjokercard"
                        android:layout_width="65dp"
                        android:layout_height="65dp"
                        android:layout_marginRight="-12dp"
                        android:layout_marginBottom="-25dp"
                        android:elevation="10dp"
                        android:src="@drawable/backside_card" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</RelativeLayout>
