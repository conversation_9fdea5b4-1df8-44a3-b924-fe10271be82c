<?xml version="1.1" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:cardCornerRadius="10dp"
        android:visibility="gone"
        app:cardPreventCornerOverlap="true">

    </androidx.cardview.widget.CardView>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:adjustViewBounds="true"
        android:layout_gravity="center"
        android:scaleType="fitXY"/>
    <LinearLayout
        android:id="@+id/opacityFilter"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#1A000000"
        android:visibility="visible"
        android:orientation="vertical" />
</FrameLayout>
