<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp40"
    android:layout_marginBottom="10dp"
    app:cardUseCompatPadding="true"
    app:cardElevation="1dp"
    app:cardCornerRadius="10dp"
    android:background="@drawable/transpernt_purple"
    android:clickable="true"
    android:foreground="?selectableItemBackground"
    android:focusable="true"
    >

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_centerVertical="true"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/lnr_parent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="10dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:elevation="2dp">

                <LinearLayout
                    android:id="@+id/lnrSerial"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.8">

                    <TextView
                        android:id="@+id/tvSerial"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:text="1"
                        android:textColor="@color/white"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/lnrDate"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1">

                    <TextView
                        android:id="@+id/tvDates"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:text="Date"
                        android:textColor="@color/white"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/lnrGame"
                    android:layout_width="0dp"
                    android:visibility="gone"
                    android:layout_height="wrap_content"
                    android:layout_weight="1">

                    <TextView
                        android:id="@+id/tvGames"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:text="3 patti"
                        android:textColor="@color/white"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/lnrCash"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.1">

                    <TextView
                        android:id="@+id/txtammount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:text="0"
                        android:textColor="@color/green"
                        android:textStyle="bold" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="16dp"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:id="@+id/txtid"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Table Id : "
                    android:textColor="@android:color/holo_blue_dark"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/txtusername"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="casual"
                    android:text="User Name"
                    android:textColor="@android:color/white"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/bulkchipsred" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="6dp"
                        android:fontFamily="monospace"
                        android:text="Ammout : "
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:textStyle="bold" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <ImageView
            android:id="@+id/imgreward"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:src="@drawable/reward"
            android:tint="@color/rewardGold"
            android:layout_centerVertical="true"
            android:visibility="gone"
            android:layout_alignParentRight="true"
            android:layout_marginRight="10dp"

            />
    </RelativeLayout>

</RelativeLayout>
