<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:id="@+id/lnrLoginContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_alignParentRight="true"
        >

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="WELCOME BACK"
            android:textColor="@color/subHeadingColor"
            android:textSize="@dimen/sp16" />

        <TextView
            android:id="@+id/login"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp20"
            android:text="@string/login"
            android:textColor="@color/headingColor"
            android:textSize="@dimen/dp22"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="7dp"
            android:gravity="center"
            android:text="Phone Number"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:visibility="visible" />

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp20"
            android:gravity="center"
            android:orientation="vertical">

            <EditText
                android:id="@+id/edtPhone"
                android:layout_width="@dimen/login_edit_width"
                android:layout_height="@dimen/login_edit_height"
                android:background="@drawable/bg_edit_box"
                android:inputType="number"
                android:maxLength="10"
                android:paddingLeft="@dimen/dp40"
                android:textColor="@color/black"
                android:textColorHint="@color/black"
                android:textSize="14sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="34dp"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:text="+91"
                android:textColor="@color/black"
                android:textStyle="bold"
                android:visibility="visible" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="220dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="10dp">

            <TextView
                android:id="@+id/imglogin"
                android:layout_width="match_parent"
                android:layout_height="@dimen/payu_dimen_45dp"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="@dimen/dp18"
                android:background="@drawable/btn_green_bg"
                android:gravity="center"
                android:text="Request OTP"
                android:textColor="@color/login_buttontxtColor"
                android:textSize="@dimen/dp18"
                android:textStyle="bold" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:onClick="openRegisterView"
            android:orientation="horizontal"
            android:visibility="gone">

            <TextView
                android:id="@+id/btnRegister"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins_medium"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="NEW TO A2Z RUMMY?"
                android:textColor="@color/subHeadingColor" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins_bold"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text=" REGISTER"
                android:textColor="@color/headingColor"
                android:textStyle="normal" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/imggoogle"
            android:layout_width="220dp"
            android:layout_height="50dp"
            android:layout_gravity="center_horizontal"
            android:gravity="center_vertical"
            android:orientation="horizontal">

                <ImageView
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_marginLeft="10dp"
                    android:src="@drawable/google_icon" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:text="Sign in with Google"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold" />
            </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="OR"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="gone">

            <androidx.cardview.widget.CardView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginVertical="10dp"
                android:visibility="gone"
                app:cardBackgroundColor="@android:color/transparent"
                app:cardCornerRadius="10dp">

                <LinearLayout
                    android:id="@+id/imgfacebook"
                    android:layout_width="250dp"
                    android:layout_height="70dp"
                    android:layout_gravity="center_vertical"
                    android:background="@drawable/login_fb_button"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"></LinearLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>
    </LinearLayout>

</RelativeLayout>
