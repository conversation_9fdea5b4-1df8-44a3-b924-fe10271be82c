<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Details.GameDetails_A"
    android:orientation="vertical"
    android:background="@drawable/home_bg2"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dp5"
        android:paddingBottom="@dimen/dp5"
        android:paddingLeft="@dimen/dimen_10dp"
        android:paddingRight="@dimen/dimen_10dp"
        >

        <ImageView
            android:id="@+id/ivBack"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/back"
            android:layout_marginRight="@dimen/dimen_10dp"
            android:onClick="onBack"
            />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Game History"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:layout_marginRight="7dp"
            />


    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:paddingRight="@dimen/dimen_10dp"
        android:paddingLeft="@dimen/dimen_10dp"
        android:layout_marginTop="@dimen/dp5"
        android:gravity="center"
        >

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recDetailsList"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dimen_10dp"
        tools:listitem="@layout/item_gamesdetails"
        tools:itemCount="1"
        android:layout_gravity="center"
        />


    </LinearLayout>
</LinearLayout>
