<?xml version="1.1" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="150dp"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical"
    android:layout_weight="1"
    >

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
    <LinearLayout
        android:layout_above="@+id/rltTotalPoint"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        >
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#3F3F3F"
        android:elevation="2dp"
        android:orientation="vertical"
        android:padding="5dp"
        >

        <TextView
            android:id="@+id/tvPlayerno"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:text="@string/player1"
            android:textColor="@color/white"
            android:gravity="center"
            />
        <TextView
            android:id="@+id/tvPlayer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:text="@string/username"
            android:textColor="@color/white"
            android:gravity="center"
            />


    </LinearLayout>

    <LinearLayout
        android:id="@+id/lnr_pointslist"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="5dp"
        >

        <include
            layout="@layout/item_point"/>
        <include
            layout="@layout/item_point"/>
        <include
            layout="@layout/item_point"/>

    </LinearLayout>

    </LinearLayout>

    <RelativeLayout
        android:id="@+id/rltTotalPoint"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:layout_alignParentBottom="true"
        android:visibility="gone"
        >

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/gray_bg"
            />

        <TextView
            android:id="@+id/tvTotalPoints"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:text="@string/total_points"
            android:textColor="@color/black"
            android:gravity="center"
            android:padding="5dp"
            android:visibility="gone"
            />

    </RelativeLayout>
    </RelativeLayout>

</androidx.core.widget.NestedScrollView>
