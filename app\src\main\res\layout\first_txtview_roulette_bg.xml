<?xml version="1.1" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardCornerRadius="@dimen/dp50"
    >
<androidx.cardview.widget.CardView
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:id="@+id/cardView"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardCornerRadius="@dimen/dp50"
    >
<RelativeLayout
    android:id="@+id/relativeChip"
    android:paddingTop="7dp"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    >

    <RelativeLayout
        android:id="@+id/rltmainview"
        android:layout_width="@dimen/dp35"
        android:layout_height="@dimen/dp35"
        android:layout_marginRight="@dimen/dimen_8dp"
        android:layout_marginLeft="4dp"
        android:layout_marginBottom="@dimen/dimen_8dp"
        android:layout_marginTop="1.5dp">
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="36"
        android:id="@+id/txt_cat"
        android:textSize="@dimen/dimen_12sp"
        android:textStyle="bold"
        android:padding="5dp"
        android:layout_centerInParent="true"
        android:shadowColor="@color/white"
        android:shadowDx="1"
        android:shadowDy="1"
        android:ellipsize="end"
        android:shadowRadius="3"
        android:textColor="@color/white"/>
    </RelativeLayout>
</RelativeLayout>
</androidx.cardview.widget.CardView>
</androidx.cardview.widget.CardView>
