<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg"
    tools:context="._Aviator.Aviator_Game_Activity">

    <ImageView
        android:id="@+id/imgback"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_marginLeft="@dimen/dp20"
        android:layout_marginTop="@dimen/dp10"
        android:src="@drawable/back"
        android:visibility="visible"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <LinearLayout
        android:id="@+id/lnr_gif"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="60dp"
        android:layout_marginTop="-42dp"
        android:layout_marginBottom="40dp">

        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/gif_roket"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/rocket_stop"
            android:visibility="visible" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="-435dp"
        android:layout_marginBottom="40dp">

        <pl.droidsonroids.gif.GifImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/starss" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/lnr_userdetails"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/lnr_gif"
        android:layout_marginTop="-55dp"
        android:gravity="center"
        android:layout_marginLeft="30dp"
        android:layout_alignParentStart="true"
        android:orientation="horizontal">

        <de.hdodenhof.circleimageview.CircleImageView
            android:id="@+id/imaprofile"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_centerVertical="true"
            android:padding="1dp"
            android:src="@drawable/pro_img" />
        <TextView
            android:id="@+id/txt_username"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:gravity="center"
            android:textColor="@color/white"
            android:textStyle="bold"
            android:layout_marginRight="20dp"
            android:textSize="17sp"/>


        <RelativeLayout
            android:layout_width="@dimen/dp140"
            android:layout_height="@dimen/dp35"
            android:layout_gravity="center"
            android:layout_marginTop="5dp"
            android:background="@drawable/player_wallet"
            android:orientation="horizontal">


            <ImageView
                android:id="@+id/imgicon"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:src="@drawable/money" />

            <TextView
                android:id="@+id/txt_wallet_amt"
                android:layout_width="@dimen/dp120"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:layout_toRightOf="@+id/imgicon"
                android:gravity="center"
                android:minWidth="80dp"
                android:text="2000"
                android:textColor="@color/yellow"
                android:textSize="16sp" />

            <ImageView
                android:id="@+id/iv_add"
                android:layout_width="25dp"
                android:layout_height="30dp"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:layout_toRightOf="@+id/txt_wallet_amt"
                android:src="@drawable/add_button"
                android:visibility="gone" />

        </RelativeLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/lnr_pay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/lnr_gif"
        android:layout_marginTop="-70dp"
        android:gravity="center"
        android:layout_marginRight="60dp"
        android:layout_alignParentEnd="true"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_into_amount"
            android:layout_width="50dp"
            android:layout_height="30dp"
            android:layout_marginRight="@dimen/dp10"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center"
            android:text="0"
            android:textSize="15dp"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/btn_pluse"
            android:layout_width="40dp"
            android:layout_height="30dp"
            android:layout_marginTop="@dimen/dp10"
            android:onClick="pluseValue"
            android:src="@drawable/add_button" />

        <TextView
            android:id="@+id/txt_inner_timer"
            android:layout_width="40dp"
            android:layout_height="30dp"
            android:gravity="center"
            android:text="1"
            android:textSize="15dp"
            android:visibility="gone" />

        <EditText
            android:id="@+id/txt_amount"
            android:layout_width="100dp"
            android:layout_height="30dp"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dp10"
            android:background="@drawable/coin_pannel"
            android:gravity="center"
            android:inputType="numberDecimal|number"
            android:maxLines="1"
            android:paddingBottom="4dp"
            android:text="10"
            android:textColor="@color/yellow"
            android:textSize="20dp" />

        <ImageView
            android:id="@+id/btn_minus"
            android:layout_width="40dp"
            android:layout_height="30dp"
            android:layout_marginTop="@dimen/dp10"
            android:onClick="minusValue"
            android:src="@drawable/reduce_button" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_waiting"
                android:layout_width="@dimen/dp150"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:visibility="invisible"
                android:text="Waiting For Next Round"
                android:textColor="@color/white"
                android:textSize="13sp" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_start"
                android:layout_width="120dp"
                android:layout_height="40dp"
                android:layout_marginLeft="@dimen/dp15"
                android:layout_marginTop="@dimen/dp5"
                android:background="@drawable/yellow_lable"
                android:text="Bet"
                android:textColor="@color/white"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

<!--    <ImageView-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_alignParentBottom="true"-->
<!--        android:layout_marginBottom="-50dp"-->
<!--        android:src="@drawable/chips_pannel" />-->

    <TextView
        android:id="@+id/txt_second"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="70dp"
        android:text="1.00x"
        android:textColor="@color/white"
        android:textSize="20dp"
        android:textStyle="bold"
        android:visibility="gone" />

    <TextView
        android:id="@+id/txt_start"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/txt_second"
        android:layout_centerHorizontal="true"
        android:text="Starts In"
        android:textColor="@color/white"
        android:textSize="20dp"
        android:visibility="gone" />


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="60dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/txt_timer"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_gravity="center"
            android:background="@drawable/clock"
            android:gravity="center"
            android:paddingLeft="2dp"
            android:shadowColor="@color/black"
            android:shadowDx="0.0"
            android:shadowDy="0.0"
            android:shadowRadius="18"
            android:text="5"
            android:textColor="@color/design_default_color_secondary_variant"
            android:textSize="20dp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/txt"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_gravity="center"
            android:gravity="center"
            android:shadowColor="@color/black"
            android:shadowDx="0.0"
            android:shadowDy="0.0"
            android:shadowRadius="18"
            android:text="Next game starts soon"
            android:textColor="@color/white"
            android:textSize="22sp"
            android:textStyle="bold"
            android:visibility="gone" />

        <TextView
            android:id="@+id/txt_exit"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_gravity="center"
            android:gravity="center"
            android:shadowColor="@color/black"
            android:shadowDx="0.0"
            android:shadowDy="0.0"
            android:shadowRadius="18"
            android:textColor="@color/white"
            android:textSize="22sp"
            android:textStyle="bold"
            android:visibility="gone" />

        <TextView
            android:id="@+id/txt_exit_value"
            android:layout_width="wrap_content"
            android:layout_height="60dp"
            android:layout_gravity="center"
            android:gravity="center"
            android:shadowColor="@color/black"
            android:shadowDx="0.0"
            android:shadowDy="0.0"
            android:shadowRadius="18"
            android:textColor="@color/white"
            android:textSize="40sp"
            android:textStyle="bold"
            android:visibility="gone" />

        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/img_start_game"
            android:layout_width="8150dp"
            android:layout_height="110dp"
            android:layout_gravity="center"
            android:src="@drawable/waiting_for_next_round"
            android:visibility="gone" />

    </LinearLayout>

</RelativeLayout>
