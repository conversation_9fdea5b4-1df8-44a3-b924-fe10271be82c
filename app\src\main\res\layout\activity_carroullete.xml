<?xml version="1.1" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_bg2"
    android:gravity="center"
    android:paddingLeft="0dp"
    android:paddingTop="0dp"
    android:paddingRight="0dp"
    android:paddingBottom="0dp"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    tools:context="._LuckJackpot.LuckJackPot_A">

    <ImageView
        android:id="@+id/imgback"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:src="@drawable/back"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:layout_width="@dimen/dimen_30dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="-640dp"
        android:layout_marginTop="-10dp"
        android:onClick="openGameRules"
        android:src="@drawable/ic_jackpot_info"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/imgback"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SpeakableTextPresentCheck,TouchTargetSizeCheck" />

    <!--<ImageView
        android:id="@+id/iv_add"
        android:layout_width="45dp"
        android:layout_height="24dp"
        android:layout_alignParentRight="true"
        android:layout_gravity="center_vertical"
        android:layout_marginTop="@dimen/dp20"
        android:layout_marginRight="@dimen/dp20"
        android:background="@drawable/iv_jackpot_add"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SpeakableTextPresentCheck,TouchTargetSizeCheck" />-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:visibility="gone">

        <Button
            android:id="@+id/btnRepeat"
            android:layout_width="@dimen/ab_button_width"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:background="@drawable/yellow_button"
            android:padding="5dp"
            android:text="REPEAT"
            android:textSize="12dp" />

        <Button
            android:id="@+id/btnDouble"
            android:layout_width="@dimen/ab_button_width"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:background="@drawable/blue_button"
            android:padding="5dp"
            android:text="DOUBLE"
            android:textSize="12dp"
            android:visibility="visible" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/lnrOnlineUser"
        android:layout_width="34dp"
        android:layout_height="34dp"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_margin="@dimen/dp20"
        android:background="@drawable/circle"
        android:elevation="@dimen/dp10"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBaseline_toBottomOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:src="@drawable/user_photo" />

        <TextView
            android:id="@+id/tvonlineuser"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="0"
            android:textColor="@color/black"
            android:textSize="10sp"
            android:textStyle="bold"
            android:visibility="visible" />
    </LinearLayout>


    <RelativeLayout
        android:id="@+id/relativeLayout2"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentStart="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        app:layout_constraintBaseline_toBottomOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <RelativeLayout
            android:id="@+id/rtlGameContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_alignParentTop="true"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="@dimen/dp30"
                android:layout_marginTop="@dimen/dp20"
                android:layout_marginRight="@dimen/dp30"
                android:layout_marginBottom="@dimen/dp60"
                android:orientation="vertical"
                app:layout_constraintRight_toRightOf="parent">
                <!-- <include
                    layout="@layout/view_carroulate_topview" />-->
                <include
                    layout="@layout/updated_view_car_roullete_topview"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignParentStart="true"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentEnd="true"
                    android:layout_alignParentBottom="true"
                    android:layout_marginStart="0dp"
                    android:layout_marginTop="-2dp"
                    android:layout_marginEnd="0dp"
                    android:layout_marginBottom="2dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:gravity="center"
                    android:text="Please"
                    android:textColor="#FF5722"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:visibility="gone" />

            </RelativeLayout>
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/lnrcarsList"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="-70dp"
            android:layout_marginEnd="@dimen/dp20"
            android:layout_marginBottom="@dimen/dp30"
            android:layout_toEndOf="@+id/rtlGameContainer"
            android:layout_toRightOf="@+id/rltBetview"
            android:gravity="center_vertical"
            android:paddingVertical="@dimen/dp50"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/rltBetview"
            app:layout_constraintTop_toTopOf="parent">

            <ScrollView
                android:id="@+id/horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/ic_carroulate_list_bg"
                android:fillViewport="true"
                android:padding="@dimen/dp10"
                android:scrollbars="none">

                <LinearLayout
                    android:id="@+id/lnrcancelist"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <include layout="@layout/item_carroulate_lastwin" />
                </LinearLayout>
            </ScrollView>
        </LinearLayout>

        <include
            layout="@layout/update_view_gameuser_bottom_strip"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/sticker_animation_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <RelativeLayout
        android:id="@+id/rltwinnersymble1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#90000000"
        android:gravity="center"
        android:visibility="gone">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true">

            <ImageView
                android:id="@+id/ivWine"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:src="@drawable/winner_patti"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvWine"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="210dp"
                android:gravity="center"
                android:text="You Win"
                android:textColor="@color/white"
                android:textSize="20sp"
                app:fontFilePath="@string/Helvetica_Bold_Extra"
                app:layout_constraintBottom_toBottomOf="@+id/ivWine"
                app:layout_constraintEnd_toEndOf="@+id/ivWine"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/ivWine" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@id/rtllosesymble1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="-30dp"
        android:background="#90000000"
        android:gravity="center"
        android:visibility="gone">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true">

            <ImageView
                android:id="@+id/ivlose"
                android:layout_width="wrap_content"
                android:layout_height="200dp"
                android:layout_centerHorizontal="true"
                android:src="@drawable/ic_game_over"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="UseAppTint" />

            <TextView
                android:id="@+id/tvlose"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="210dp"
                android:gravity="center"
                android:text=""
                android:textColor="@color/BrownColor"
                android:textSize="20sp"
                app:fontFilePath="@string/Helvetica_Bold_Extra"
                app:layout_constraintBottom_toBottomOf="@+id/ivlose"
                app:layout_constraintEnd_toEndOf="@+id/ivlose"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/ivlose" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="@dimen/dp80"
        android:layout_height="@dimen/dp80"
        android:layout_alignParentRight="true"
        android:layout_marginRight="@dimen/dp60"
        android:layout_weight="1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:layout_width="@dimen/dp80"
            android:layout_height="@dimen/dp80"
            android:layout_centerHorizontal="true"
            android:scaleType="centerCrop"
            android:src="@drawable/watch"
            android:visibility="visible" />

        <TextView
            android:id="@+id/txtcountdown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:shadowColor="#5d5534"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/payu_dimen_28dp"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="3"
            android:text=""
            android:textColor="#5d5534"
            android:textSize="@dimen/dp20"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tvStartTimerNew"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:shadowColor="#5d5534"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/payu_dimen_28dp"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="3"
            android:text="0"
            android:textColor="#5d5534"
            android:textSize="@dimen/dp20"
            android:visibility="visible" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        app:layout_constraintBottom_toTopOf="@+id/relativeLayout2">

        <!--        <TextView-->
        <!--            android:id="@+id/txtGameRunning"-->
        <!--            android:layout_width="wrap_content"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:layout_centerHorizontal="true"-->
        <!--            android:gravity="center"-->
        <!--            android:text="Please"-->
        <!--            android:textColor="#EEC283"-->
        <!--            android:textSize="16sp"-->
        <!--            android:textStyle="bold"-->
        <!--            android:visibility="visible" />-->

        <!-- <TextView
             android:id="@+id/txtGameBets"
             android:layout_width="wrap_content"
             android:layout_height="wrap_content"
             android:layout_centerHorizontal="true"
             android:gravity="center"
             android:text=""
             android:background="@drawable/iv_bet_begin"
             android:textColor="#EEC283"
             android:textSize="16sp"
             android:textStyle="bold"
             android:visibility="visible" />-->
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rltOngoinGame"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:background="#90000000"
        android:gravity="center"
        android:visibility="gone">

        <ImageView
            android:id="@+id/txtGameRunning"
            android:layout_width="@dimen/dp300"
            android:layout_height="@dimen/dp50"
            android:src="@drawable/waiting_for_next"
            android:visibility="visible" />


    </RelativeLayout>

    <ImageView
        android:id="@+id/txtGameBets"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:src="@drawable/place_your_bet"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>
