package com.gamegards.luckyrbm.MyAccountDetails;

public class MyWinnigmodel {


    public  String id;
    public  String table_id;
    public  String amount;
    public  int invest = 0;
    public  String winner_id;
    public  String name;
    public  String totalwin;
    public  String userimage;
    public  String level;
    public  String purchase_user_id;
    public  String added_date = "";
    public  String game_type = "";
    public String bet = "";
    public String room_id = "";
    public String ander_baher_id = "";
    public String winning_amount = "";
    public String transactionid = "";
    public int ViewType = 0;


    public String updated_date;
    public String price;
    public String coin;


    public static final int REDEEM_LIST = 0;
    public static final int TRANSACTION_LIST = 1;

    public String getUpdated_date() {
        return updated_date;
    }

    public void setUpdated_date(String updated_date) {
        this.updated_date = updated_date;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getCoin() {
        return coin;
    }

    public void setCoin(String coin) {
        this.coin = coin;
    }

    public String getLevel() {
        return level;
    }
    public void setLevel(String level) {
        this.level = level;
    }

    public String getPurchase_user_id() {
        return purchase_user_id;
    }

    public void setPurchase_user_id(String purchase_user_id) {
        this.purchase_user_id = purchase_user_id;
    }
    public String getAdded_date() {
        return added_date;
    }
    public void setAdded_date(String added_date) {
        this.added_date = added_date;
    }

    public String getBet() {
        return bet;
    }

    public void setBet(String bet) {
        this.bet = bet;
    }

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public String getAnder_baher_id() {
        return ander_baher_id;
    }

    public void setAnder_baher_id(String ander_baher_id) {
        this.ander_baher_id = ander_baher_id;
    }

    public String getWinning_amount() {
        return winning_amount;
    }

    public void setWinning_amount(String winning_amount) {
        this.winning_amount = winning_amount;
    }

    public String getTransactionid() {
        return transactionid;
    }

    public void setTransactionid(String transactionid) {
        this.transactionid = transactionid;
    }

    public static final String TEEN_PATTI = "3 Patti";
    public static final String RUMMY = "Rummy";
    public static final String RUMMY_POOL = "Rummy Pool";
    public static final String RUMMY_DEAL = "Rummy Deal";
    public static final String Andhar_Bahar = "Andar Bahar";
    public static final String DRAGON_TIGER = "Dragon Tiger";

    public static final String SEVEN_UP_DOWN = "7UpDown";
    public static final String JACKPOT_3PATTI = "Jackpot3patti";
    public static final String ANIMAL_ROULLETE = "Animal Roulette";
    public static final String CAR_ROULLETE = "Car Roulette";
    public static final String HEAD_TAIL = "Head & Tail";
    public static final String COLOR_PREDICTION = "Color Prediction";
    public static final String BACCARATE = "Baccarate";
    public static final String JHANDI_MUNDA = "Jhandi Munda";
    public static final String ROULETTE = "Roulette";
    public static final String POKER = "Poker";
    public static final String RED_BLACK = "Red vs Black";
    public static final String AVIATOR = "Aviator";
    public static final String LUDO = "Ludo";

    public String getUserimage() {
        return userimage;
    }

    public void setUserimage(String userimage) {
        this.userimage = userimage;
    }

    public String getTotalwin() {
        return totalwin;
    }

    public void setTotalwin(String totalwin) {
        this.totalwin = totalwin;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTable_id() {
        return table_id;
    }

    public void setTable_id(String table_id) {
        this.table_id = table_id;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getWinner_id() {
        return winner_id;
    }

    public void setWinner_id(String winner_id) {
        this.winner_id = winner_id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
