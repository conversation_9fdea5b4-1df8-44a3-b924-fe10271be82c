<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/dialogParentStyle"
    tools:context=".Activity.BuyChipsList">

    <RelativeLayout
        android:id="@+id/rlt_parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>


    <ImageView
        android:id="@+id/imgback"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:src="@drawable/back"
        android:visibility="gone" />

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        >

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            style="@style/popUpBoxbg"
            android:orientation="vertical"
            android:id="@+id/lnr_box"
            android:layout_marginTop="-60dp"
            android:layout_below="@+id/rtl_toolbar"
            android:layout_marginHorizontal="20dp"
            >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="visible"
                android:layout_marginVertical="10dp"
                android:layout_marginHorizontal="35dp"
                >

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rec_history"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:listitem="@layout/chips_buy_layout"
                    android:visibility="gone"
                    tools:itemCount="1"
                    />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:textAllCaps="true"
                    android:textColor="#0B0A0A"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:visibility="visible" />
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/dp50"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:layout_gravity="center"
                        android:text="Enter Coins"
                        android:textColor="#F44336"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:visibility="visible" />

                    <LinearLayout
                        android:layout_width="250dp"
                        android:layout_height="50dp"
                        android:paddingRight="@dimen/dp30"
                        android:paddingLeft="@dimen/dp30"
                        android:background="@drawable/coupon">

                        <EditText
                            android:id="@+id/txtamount"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_vertical"
                            android:hint="Enter Coins To Add"
                            android:background="@null"
                            android:inputType="number"
                            android:textColorHint="@color/white"
                            android:textColor="@color/colordullwhite"
                            android:textSize="16sp" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/dp20"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:layout_gravity="center"
                        android:text="USDT Amount"
                        android:textColor="#0B0A0A"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:visibility="visible" />

                    <LinearLayout
                        android:layout_width="250dp"
                        android:layout_height="50dp"
                        android:paddingRight="@dimen/dp30"
                        android:paddingLeft="@dimen/dp30"
                        android:background="@drawable/coupon">

                        <TextView
                            android:id="@+id/cryptocoinss"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_vertical"
                            android:hint="₹"
                            android:gravity="center_vertical"
                            android:background="@null"
                            android:inputType="number"
                            android:enabled="false"
                            android:textColorHint="@color/white"
                            android:textColor="@color/colordullwhite"
                            android:textSize="16sp" />

                    </LinearLayout>

                </LinearLayout>
<!--                <TextView-->
<!--                    android:id="@+id/cryptocoins"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="20dp"-->
<!--                    android:layout_marginTop="@dimen/dp10"-->
<!--                    android:text="0 coins"-->
<!--                    android:textAlignment="center"-->
<!--                    android:textAllCaps="true"-->
<!--                    android:textColor="@color/purple"-->
<!--                    android:textSize="15sp"-->
<!--                    android:textStyle="bold"-->
<!--                    android:visibility="visible" />-->
                <TextView
                    android:id="@+id/btn_add"
                    android:layout_width="130dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center_horizontal"
                    android:background="@drawable/ic_button"
                    android:gravity="center"
                    android:text="ADD"
                    android:layout_marginTop="@dimen/dp20"
                    android:textColor="@color/BrownColor"
                    android:textSize="20dp" />


            </LinearLayout>

            <TextView
                android:id="@+id/txtnotfound"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="Data no available!"
                android:gravity="center"
                android:visibility="gone"
                />

            <RelativeLayout
                android:id="@+id/rlt_progress"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ProgressBar
                    android:id="@+id/progressBar"
                    style="?android:attr/progressBarStyle"
                    android:layout_width="300dp"
                    android:layout_height="100dp"
                    android:layout_centerInParent="true"
                    android:layout_gravity="center"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:indeterminateDrawable="@drawable/cpb_3"
                    android:indeterminateDuration="4000"
                    android:visibility="gone" />
            </RelativeLayout>





        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/rtl_toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp15">

            <ImageView
                android:id="@+id/imgclosetop"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_alignParentRight="true"
                android:layout_alignBottom="@id/img_diaProfile"
                android:layout_marginEnd="@dimen/dp50"
                android:src="@drawable/ic_close_new"
                android:visibility="visible" />

            <RelativeLayout
                android:id="@+id/img_diaProfile"
                android:layout_width="150dp"
                android:layout_height="45dp"
                android:layout_centerHorizontal="true"
                android:background="@drawable/bg_gredient"
                android:gravity="center"
                android:src="@drawable/app_logo_second"
                android:visibility="visible">

                <TextView
                    android:id="@+id/txtheader"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="Settings"
                    android:textColor="@color/white"
                    android:textStyle="bold" />
            </RelativeLayout>
        </RelativeLayout>


    </RelativeLayout>


    <LinearLayout
        android:id="@+id/linear_no_history"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone"
        android:orientation="vertical">
        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_gravity="center"
            android:layout_margin="10dp"
            android:tint="@color/colorAccent"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/colordullwhite"
            android:textSize="16sp"
            android:gravity="center"
            android:visibility="gone"
            android:text="No Winning History is Available"/>
    </LinearLayout>
</RelativeLayout>
