<?xml version="1.1" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Account.LoginScreen"
    >

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/imgBackground"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/login_bg" />

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center">

<!--            <ImageView-->
<!--                android:id="@+id/imgBackgroundlogin"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="match_parent" />-->

            <!-- Logo Section -->
            <ImageView
                android:layout_width="200dp"
                android:layout_height="200dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="80dp"
                android:layout_marginBottom="30dp"
                android:padding="20dp"
                android:src="@drawable/app_logo_second"
                android:visibility="visible" />

            <!-- Login Container -->
            <RelativeLayout
                android:id="@+id/rltLoginContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center">

                <RelativeLayout
                    android:id="@+id/lnr"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center">

                    <ScrollView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:fillViewport="true"
                        android:scrollbars="none"
                        android:visibility="visible">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">

                            <LinearLayout
                                android:id="@+id/lnrLoginContainer"
                                android:layout_width="320dp"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="20dp"
                                android:layout_marginRight="20dp"
                                android:layout_marginBottom="50dp"
                                android:orientation="vertical"
                                android:gravity="center">
                                <ViewFlipper
                                    android:id="@+id/loginFlipper"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:visibility="visible"
                                    android:layout_gravity="right"
                                    >
                                    <include
                                        layout="@layout/view_login_sendotp"/>
                                    <include
                                        layout="@layout/view_login_enter_otp"/>

                                </ViewFlipper>

                                <ViewFlipper
                                    android:id="@+id/Signupviewfliper"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:visibility="gone"
                                    >

                                    <include
                                        layout="@layout/view_login_signup_sendotp"/>
                                    <include
                                        layout="@layout/view_login_enter_otp"/>
                                </ViewFlipper>
                            </LinearLayout>
                        </RelativeLayout>

                    </ScrollView>
                </RelativeLayout>

            </RelativeLayout>

        </LinearLayout>
    </RelativeLayout>

    <include layout="@layout/bottom_reffercode" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
