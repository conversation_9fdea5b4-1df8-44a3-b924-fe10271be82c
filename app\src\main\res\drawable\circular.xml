<?xml version="1.1" encoding="utf-8"?>

<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:id="@android:id/secondaryProgress">
        <shape
            android:innerRadiusRatio="2.5"
            android:shape="ring"
            android:thicknessRatio="15.0"
            android:useLevel="true">


            <gradient
                android:centerColor="#999999"
                android:endColor="#999999"
                android:startColor="#999999"
                android:type="sweep" />
        </shape>
    </item>

    <item android:id="@android:id/progress">
        <rotate
            android:fromDegrees="270"
            android:pivotX="50%"
            android:pivotY="50%"
            android:toDegrees="270">

            <shape
                android:innerRadiusRatio="2.5"
                android:shape="ring"
                android:thicknessRatio="15.0"
                android:useLevel="true">


                <rotate
                    android:fromDegrees="0"
                    android:pivotX="50%"
                    android:pivotY="50%"
                    android:toDegrees="360" />

                <gradient
                    android:centerColor="#59EC09"
                    android:endColor="#59EC09"
                    android:startColor="#59EC09"
                    android:type="sweep" />

            </shape>
        </rotate>
    </item>
</layer-list>
