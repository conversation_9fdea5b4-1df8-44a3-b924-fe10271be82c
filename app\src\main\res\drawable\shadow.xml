<?xml version="1.1" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <item
        android:left="13dp"
        android:right="13dp"
        android:top="0dp"
        android:bottom="0dp"
        >
        <shape android:shape="rectangle">
<!--            <solid android:color="@color/shadow"/>-->

            <gradient
                android:angle="-180"
                android:endColor="#72000000"
                android:startColor="@android:color/transparent" />

            <corners
                android:radius="5dp"/>
            <!--shadow Color-->
        </shape>
    </item>

<!--    <item-->
<!--        android:left="0dp"-->
<!--        android:right="10dp"-->
<!--        android:top="0dp"-->
<!--        android:bottom="3dp"-->
<!--        >-->
<!--        <shape android:shape="rectangle">-->
<!--            <solid android:color="@android:color/transparent"/>//Background Color-->
<!--            <corners-->
<!--                android:radius="5dp"/>-->
<!--        </shape>-->
<!--    </item>-->

</layer-list>
