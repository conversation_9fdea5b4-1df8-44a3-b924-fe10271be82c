<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    style="@style/dialogParentStyle">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:id="@+id/lnr_box"
        android:background="@drawable/home_bg2"
        android:layout_below="@+id/rtl_toolbar"
        android:layout_marginTop="@dimen/pop_up_top_margin"
        android:layout_marginHorizontal="25dp"
        android:padding="20dp"
        >

        <Button
            android:id="@+id/btn_apply"
            android:layout_width="279dp"
            android:layout_height="@dimen/dp50"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp25"
            android:background="@drawable/orange_btn"
            android:gravity="center"
            android:paddingBottom="@dimen/dp7"
            android:text="Apply Withdrawal"
            android:textSize="18sp"
            android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/lnrRuleslist"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="@dimen/dp5"
                android:paddingBottom="@dimen/dp20"
                >


                <include
                    layout="@layout/view_color_withdraw_heading"/>

                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    >
                    <LinearLayout
                        android:id="@+id/lnrlastView"
                        android:layout_width="match_parent"
                        android:paddingRight="@dimen/dp5"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        >
                        <include
                            layout="@layout/view_color_lastwin_list"/>
                    </LinearLayout>
                </ScrollView>
            </LinearLayout>



    </LinearLayout>


    <include
        layout="@layout/dialog_toolbar"/>


</RelativeLayout>
