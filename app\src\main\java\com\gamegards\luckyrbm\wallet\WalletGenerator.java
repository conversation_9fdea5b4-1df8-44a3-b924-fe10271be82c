package com.gamegards.luckyrbm.wallet;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;
import android.widget.Toast;

import com.android.volley.AuthFailureError;
import com.android.volley.Request;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;
import com.gamegards.luckyrbm.ApiClasses.Const;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

import static com.gamegards.luckyrbm.Activity.Homepage.MY_PREFS_NAME;

public class WalletGenerator {
    
    public static void generateWallet(Context context) {
        StringRequest stringRequest = new StringRequest(Request.Method.POST, Const.GENERATE_WALLET,
                new Response.Listener<String>() {
                    @Override
                    public void onResponse(String response) {
                        Log.d("WalletGenerator", "Response: " + response);
                        try {
                            JSONObject jsonObject = new JSONObject(response);
                            String code = jsonObject.optString("code", "");
                            String message = jsonObject.optString("message", "");
                            
                            if (code.equals("200")) {
                                String walletAddress = jsonObject.optString("wallet_address", "");
                                if (!walletAddress.isEmpty()) {
                                    SharedPreferences prefs = context.getSharedPreferences(MY_PREFS_NAME, Context.MODE_PRIVATE);
                                    SharedPreferences.Editor editor = prefs.edit();
                                    editor.putString("wallet_address", walletAddress);
                                    editor.apply();
                                    
                                    Toast.makeText(context, "Wallet generated successfully", Toast.LENGTH_SHORT).show();
                                    Log.d("WalletGenerator", "Wallet address: " + walletAddress);
                                }
                            } else {
                                Toast.makeText(context, "Failed to generate wallet: " + message, Toast.LENGTH_SHORT).show();
                            }
                        } catch (JSONException e) {
                            Log.e("WalletGenerator", "JSON parsing error", e);
                            Toast.makeText(context, "Error processing response", Toast.LENGTH_SHORT).show();
                        }
                    }
                }, new Response.ErrorListener() {
            @Override
            public void onErrorResponse(VolleyError error) {
                Log.e("WalletGenerator", "Network error", error);
                Toast.makeText(context, "Network error", Toast.LENGTH_SHORT).show();
            }
        }) {
            @Override
            public Map<String, String> getHeaders() throws AuthFailureError {
                HashMap<String, String> headers = new HashMap<>();
                headers.put("token", Const.TOKEN);
                return headers;
            }

            @Override
            protected Map<String, String> getParams() throws AuthFailureError {
                SharedPreferences prefs = context.getSharedPreferences(MY_PREFS_NAME, Context.MODE_PRIVATE);
                HashMap<String, String> params = new HashMap<>();
                params.put("user_id", prefs.getString("user_id", ""));
                params.put("token", prefs.getString("token", ""));
                return params;
            }
        };

        Volley.newRequestQueue(context).add(stringRequest);
    }
}