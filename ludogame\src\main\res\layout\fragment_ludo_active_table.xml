<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center">

    <RelativeLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/background_new"
        >

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            >

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:background="@drawable/ludolisting"
                android:id="@+id/lnr_box"
                android:layout_marginTop="-20dp"
                android:layout_below="@+id/rtl_toolbar"
                android:layout_marginHorizontal="25dp"
                android:padding="20dp"

                >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:visibility="visible"
                    android:layout_marginBottom="30dp"
                    android:layout_marginTop="40dp"
                    >

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="#241240"
                        android:elevation="2dp"
                        >

                        <LinearLayout
                            android:id="@+id/lnrHeadBoot"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            >

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:ellipsize="end"
                                android:text="Boot"
                                android:textColor="@color/white"
                                android:gravity="center"
                                />

                        </LinearLayout>


                        <View
                            android:layout_width="1dp"
                            android:layout_height="match_parent"
                            android:background="?dividerHorizontal"
                            />



                        <View
                            android:layout_width="1dp"
                            android:layout_height="match_parent"
                            android:background="?dividerHorizontal"
                            />


                        <LinearLayout
                            android:id="@+id/lnrHeadPlayer"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            >

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:ellipsize="end"
                                android:text="Total Players"
                                android:textColor="@color/white"
                                android:gravity="center"
                                />

                        </LinearLayout>

                        <View
                            android:layout_width="1dp"
                            android:layout_height="match_parent"
                            android:background="?dividerHorizontal"
                            />

                        <LinearLayout
                            android:id="@+id/lnrHeadJoin"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1.3"
                            >

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:ellipsize="end"
                                android:text="Join"
                                android:textColor="@color/white"
                                android:gravity="center"
                                />

                        </LinearLayout>


                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rec_user_points"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        tools:listitem="@layout/item_ludo_active_table"
                        />


                </LinearLayout>

                <TextView
                    android:id="@+id/txtnotfound"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="Data no available!"
                    android:gravity="center"
                    android:visibility="gone"
                    />

                <RelativeLayout
                    android:id="@+id/rlt_progress"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"
                    >

                    <ProgressBar
                        android:id="@+id/progressBar"
                        android:layout_width="300dp"
                        android:layout_height="100dp"
                        style="?android:attr/progressBarStyle"
                        android:indeterminateDuration="4000"
                        android:layout_marginBottom="10dp"
                        android:layout_marginTop="10dp"
                        android:layout_gravity="center"
                        android:layout_centerInParent="true"
                        android:visibility="visible"
                        />

                </RelativeLayout>





            </RelativeLayout>


            <RelativeLayout
                android:id="@+id/rtl_toolbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:layout_marginRight="10dp">

                <ImageView
                    android:id="@+id/imgclosetop"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_alignParentRight="true"
                    android:src="@drawable/cross_btn"
                    android:visibility="visible" />

                <RelativeLayout
                    android:id="@+id/img_diaProfile"
                    android:layout_width="250dp"
                    android:layout_height="45dp"
                    android:layout_centerHorizontal="true"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/txt_title"
                        android:layout_width="200dp"
                        android:layout_height="40dp"
                        android:layout_centerHorizontal="true"
                        android:background="@drawable/play_local_written"
                        android:backgroundTint="@color/yellow3"
                        android:elegantTextHeight="true"
                        android:fontFamily="@font/oswald"
                        android:gravity="center"
                        android:visibility="gone"
                        android:text="Ludo Listing"
                        android:textColor="@color/purple3"
                        android:textSize="20dp"
                        android:textStyle="bold" />


                </RelativeLayout>

            </RelativeLayout>



        </RelativeLayout>



    </RelativeLayout>

</RelativeLayout>
