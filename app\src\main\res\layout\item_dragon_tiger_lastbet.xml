<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_marginTop="@dimen/dp5"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/ivlastwinbg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignLeft="@id/tvItems"
        android:layout_alignRight="@id/tvItems"
        android:layout_alignBottom="@id/tvItems"
        android:layout_alignTop="@id/tvItems"
        android:background="@drawable/ic_jackpot_strip_green"
        android:visibility="gone"
        />

    <TextView
        android:id="@+id/tvItems"
        style="@style/ShadowWhiteTextview"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:paddingRight="@dimen/dp40"
        android:paddingLeft="@dimen/dp40"
        android:singleLine="true"
        android:background="@drawable/join_one_update"
        android:ellipsize="end"
        android:gravity="center"
        android:text="High"
        android:textSize="12sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvItems2"
        style="@style/ShadowWhiteTextview"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:paddingRight="@dimen/dp40"
        android:layout_marginLeft="@dimen/dp5"
        android:singleLine="true"
        android:background="@drawable/color_small"
        android:layout_toRightOf="@+id/tvItems"
        android:ellipsize="end"
        android:gravity="center"
        android:text="High"
        android:visibility="visible"
        android:textSize="12sp"
        android:textStyle="bold" />


</RelativeLayout>
