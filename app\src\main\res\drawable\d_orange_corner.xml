<?xml version="1.1" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle"
    >


    <item android:state_pressed="false">

        <shape
            android:shape="rectangle">

            <solid
                android:color="@color/colorPrimary"/>

            <corners
                android:radius="3dp"/>

        </shape>

    </item>

    <item android:state_pressed="true">

        <shape
            android:shape="rectangle">

            <solid
                android:color="@color/color2"/>

            <corners
                android:radius="3dp"/>

        </shape>

    </item>




</selector>
