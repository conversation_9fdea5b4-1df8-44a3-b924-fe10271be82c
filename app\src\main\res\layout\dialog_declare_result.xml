<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center">


    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="50dp"
        app:cardCornerRadius="10dp"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:background="#222222"
                >

                <TextView
                    android:id="@+id/txt_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_gravity="center_vertical"
                    android:text="Result | Game ID : "
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    />

                <ImageView
                    android:id="@+id/imgclosetop"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:src="@drawable/ic_close_new"
                    android:visibility="visible"
                    android:elevation="2dp"
                    android:layout_gravity="center_vertical"
                    />


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#3F3F3F"
                android:elevation="2dp"
                >

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.8"
                    >

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:text="Username"
                        android:textColor="@color/white"
                        android:gravity="center"
                        />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    >

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:text="Result"
                        android:textColor="@color/white"
                        android:gravity="center"
                        />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    >

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:text="Cards"
                        android:textColor="@color/white"
                        android:gravity="center"
                        />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5"
                    >

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:text="Score"
                        android:textColor="@color/white"
                        android:gravity="center"
                        />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/lnrWin"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5"
                    >

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:text="Won"
                        android:textColor="@color/white" />

                </LinearLayout>
                <LinearLayout
                    android:id="@+id/lnrTotal"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5"
                    >

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:text="Total"
                        android:textColor="@color/white" />

                </LinearLayout>


            </LinearLayout>

            <LinearLayout

                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/box_winbg"
                android:orientation="vertical"
                android:id="@+id/lnr_box"
                android:gravity="center"
                android:layout_centerInParent="true"
                android:padding="10dp"
                android:layout_marginTop="-10dp"
                android:layout_marginBottom="-10dp"
                >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="@color/white"
                    android:paddingBottom="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    >

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rec_declareresult"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#035C08"
                android:elevation="2dp"
                >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_gravity="center"
                    >

                    <TextView
                        android:id="@+id/txt_timer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:text="Get Ready - Next game start in 0 second(s)"
                        android:textColor="@color/white"
                        android:gravity="center"
                        />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginRight="20dp"
                    >

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:text="Joker"
                        android:textColor="@color/white"
                        android:gravity="center"
                        android:layout_gravity="center"
                        android:textSize="15sp"
                        />

                    <ImageView
                        android:id="@+id/imgjokercard"
                        android:layout_width="65dp"
                        android:layout_height="65dp"
                        android:layout_marginRight="-12dp"
                        android:layout_marginBottom="-25dp"
                        android:src="@drawable/bla"
                        android:elevation="10dp"
                        />

                </LinearLayout>


            </LinearLayout>

        </LinearLayout>
    </androidx.cardview.widget.CardView>


</RelativeLayout>
