<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/rltslidshow"
        android:layout_width="match_parent"
        android:layout_height="150dp"
        android:layout_centerHorizontal="true"
        android:layout_centerInParent="true"
        android:background="@drawable/gamebuttonbg"
        android:visibility="visible"
        >

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:orientation="vertical"
            android:gravity="center"
            >

            <TextView
                android:id="@+id/tvHeading"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/share_wallet"
                android:textColor="@color/red"
                android:layout_gravity="center"
                android:gravity="center"
                android:layout_marginBottom="10dp"
                android:textStyle="bold"
                android:textSize="17dp"/>

            <TextView
                android:id="@+id/txtSlidshow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/someone_has_asking_for_share_wallet"
                android:textColor="@color/white"
                android:layout_gravity="center_vertical"
                android:textSize="17dp"/>

            <TextView
                android:id="@+id/tvSlideCountDown"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/_2_s"
                android:textColor="@color/lghtAccent"
                android:layout_gravity="center"
                android:layout_marginBottom="20dp"
                android:gravity="center"
                android:textSize="17dp"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_gravity="center"
                android:gravity="center"
                >
                <TextView
                    android:id="@+id/imgclose"
                    android:layout_width="wrap_content"
                    android:layout_height="34dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="10dp"
                    android:tint="@color/white"
                    android:visibility="visible"
                    android:background="@drawable/btn_oragen_drop"
                    android:text="Cancel"
                    android:paddingRight="15dp"
                    android:paddingLeft="15dp"
                    android:gravity="center"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    />

                <TextView
                    android:id="@+id/imgaccespt"
                    android:layout_width="wrap_content"
                    android:layout_height="34dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="10dp"
                    android:tint="@color/white"
                    android:visibility="visible"
                    android:background="@drawable/btn_green_bg"
                    android:text="Accept"
                    android:paddingRight="15dp"
                    android:paddingLeft="15dp"
                    android:gravity="center"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    />
            </LinearLayout>


        </LinearLayout>




    </RelativeLayout>


</RelativeLayout>
