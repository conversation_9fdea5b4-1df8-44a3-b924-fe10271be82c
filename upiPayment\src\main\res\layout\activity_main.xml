<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/background_layout"
    android:background="@drawable/buildbg"
    android:orientation="vertical"
    tools:context=".MainActivity">

    <LinearLayout
        android:id="@+id/layout_profile"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/txt_rs"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="50dp"
            android:layout_marginLeft="25dp"
            android:background="@drawable/editbg"
            android:text="Rs."
            android:textColor="@color/black"
            android:textSize="16dp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/txt_upi"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/txt_rs"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="25dp"
            android:layout_marginLeft="25dp"
            android:background="@drawable/editbg"
            android:text="UPI ID"
            android:textColor="@color/black"
            android:textSize="16dp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/txt_ref"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/txt_upi"
            android:layout_marginLeft="25dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="25dp"
            android:background="@drawable/editbg"
            android:text="Ref No."
            android:textColor="@color/black"
            android:textSize="16dp"
            android:textStyle="bold" />

    </LinearLayout>
</RelativeLayout>
