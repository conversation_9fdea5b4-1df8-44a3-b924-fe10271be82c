<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        >
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"

        android:orientation="vertical"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            >
            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_marginTop="@dimen/dp1"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/txt_no"
                    android:text="1"
                    android:layout_width="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="@dimen/dp15"
                    android:layout_marginLeft="@dimen/dp10"
                    android:layout_height="wrap_content"/>

                <TextView
                    android:id="@+id/txt_name"
                    android:text="@string/app_name"
                    android:layout_width="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="@dimen/dp15"
                    android:layout_marginLeft="@dimen/dp10"
                    android:layout_height="wrap_content"/>

                <TextView
                    android:id="@+id/txt_details"
                    android:text="Details >"
                    android:layout_width="match_parent"
                    android:layout_gravity="end"
                    android:gravity="end"
                    android:visibility="gone"
                    android:textSize="@dimen/dimen_16sp"
                    android:textColor="@color/gray_bg"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
        </LinearLayout>
        <View android:background="@color/gray"
            android:layout_width = "match_parent"
            android:layout_marginLeft="@dimen/dp20"
            android:layout_marginRight="@dimen/dp20"
            android:layout_height="1dp"/>

    </LinearLayout>
  </LinearLayout>
</LinearLayout>
